# Augment Code 智能体系统配置指南

本指南详细介绍如何在Augment Code中配置和使用智能体系统。

## 📋 目录

1. [系统要求](#系统要求)
2. [安装配置](#安装配置)
3. [智能体配置](#智能体配置)
4. [提示词管理](#提示词管理)
5. [MCP工具集成](#mcp工具集成)
6. [使用方法](#使用方法)
7. [故障排除](#故障排除)

## 🔧 系统要求

### 基础要求
- **Augment Code**: 最新版本的VSCode扩展
- **Node.js**: 版本 14.0 或更高
- **操作系统**: Windows 10+, macOS 10.15+, Linux (Ubuntu 18.04+)

### 可选要求
- **FuniA0Mcp工具**: 用于产品管理和技术方案设计
- **Git**: 用于版本控制集成
- **Xcode**: 用于iOS开发相关功能

## 🚀 安装配置

### 1. 基础安装

```bash
# 克隆或下载智能体系统到项目目录
cd your-project-directory
git clone <智能体系统仓库> augment-agents

# 安装依赖
cd augment-agents
npm install
```

### 2. 初始化配置

```javascript
// 在项目中引入智能体系统
const { getGlobalInstance } = require('./augment-agents/src/index');

// 初始化系统
const agentSystem = getGlobalInstance({
    configPath: './augment-agents/config',
    enableMCP: true,
    autoInit: true
});
```

### 3. Augment Code集成

在Augment Code设置中添加智能体系统配置：

```json
{
    "augment.agents": {
        "enabled": true,
        "configPath": "./augment-agents/config",
        "defaultAgent": "ios-developer",
        "showSelector": true
    }
}
```

## 🤖 智能体配置

### 智能体配置文件结构

```json
{
    "id": "智能体唯一标识",
    "name": "智能体显示名称",
    "description": "智能体功能描述",
    "version": "1.0.0",
    "language": "zh-CN",
    "category": "分类标签",
    "icon": "📱",
    "tags": ["标签1", "标签2"],
    
    "prompts": {
        "system": "系统提示词文件路径",
        "welcome": "欢迎提示词文件路径",
        "context": "上下文提示词文件路径"
    },
    
    "mcpTools": [
        "FuniA0Mcp",
        "file-manager",
        "terminal"
    ],
    
    "capabilities": [
        "code-analysis",
        "debugging",
        "architecture-design"
    ],
    
    "settings": {
        "autoRun": true,
        "maxTokens": 4000,
        "temperature": 0.7
    }
}
```

### 内置智能体

系统提供以下内置智能体：

#### 1. iOS开发专家 (`ios-developer`)
- **专长**: iOS应用开发、音视频处理、虚拟摄像头
- **技术栈**: Objective-C, Swift, AVFoundation, CoreMedia
- **适用场景**: iOS项目开发、代码分析、性能优化

#### 2. 代码审查员 (`code-reviewer`)
- **专长**: 代码质量分析、安全检测、最佳实践
- **功能**: 静态分析、漏洞检测、性能评估
- **适用场景**: 代码审查、质量保证、安全检查

#### 3. 项目经理 (`project-manager`)
- **专长**: 项目规划、任务管理、进度跟踪
- **方法论**: 敏捷开发、瀑布模型、看板方法
- **适用场景**: 项目管理、需求分析、团队协作

### 创建自定义智能体

```javascript
const customAgent = {
    id: 'my-custom-agent',
    name: '我的自定义智能体',
    description: '针对特定需求的智能助手',
    category: 'custom',
    
    prompts: {
        system: 'prompts/zh-CN/custom/system.md'
    },
    
    mcpTools: ['FuniA0Mcp'],
    capabilities: ['custom-task']
};

await agentSystem.createCustomAgent(customAgent);
```

## 📝 提示词管理

### 提示词文件结构

```
config/prompts/
├── zh-CN/                 # 中文提示词
│   ├── ios/              # iOS开发相关
│   │   ├── system.md     # 系统提示词
│   │   ├── welcome.md    # 欢迎提示词
│   │   └── context.md    # 上下文提示词
│   ├── review/           # 代码审查相关
│   └── pm/               # 项目管理相关
└── templates/            # 通用模板
```

### 提示词变量

支持在提示词中使用变量：

```markdown
# 欢迎使用{{agentName}}

当前时间：{{timestamp}}
项目名称：{{projectName}}
用户名称：{{userName}}
```

### 变量列表

- `{{agentName}}`: 智能体名称
- `{{agentId}}`: 智能体ID
- `{{timestamp}}`: 当前时间戳
- `{{date}}`: 当前日期
- `{{time}}`: 当前时间
- `{{language}}`: 系统语言
- 自定义变量：通过上下文传入

## 🔧 MCP工具集成

### FuniA0Mcp工具配置

```json
{
    "mcp": {
        "enableIntegration": true,
        "funiA0McpIntegration": {
            "enabled": true,
            "priority": "high"
        }
    }
}
```

### 可用的FuniA0Mcp功能

1. **技术方案设计** (`technical-proposal`)
   ```javascript
   await agentSystem.callMCPTool('FuniA0Mcp', 'technical-proposal', {
       projectType: 'iOS应用',
       requirements: '功能需求描述'
   });
   ```

2. **产品经理自动配置** (`pm-auto-config`)
   ```javascript
   await agentSystem.callMCPTool('FuniA0Mcp', 'pm-auto-config', {
       action: 'set-prd',
       path: './docs/PRD.md'
   });
   ```

3. **页面自动生成** (`pm-auto-generate`)
   ```javascript
   await agentSystem.callMCPTool('FuniA0Mcp', 'pm-auto-generate', {
       action: 'start'
   });
   ```

4. **UI/UX设计** (`ui-ux-design`)
   ```javascript
   await agentSystem.callMCPTool('FuniA0Mcp', 'ui-ux-design', {
       trigger: 'generate'
   });
   ```

### 工具兼容性检查

```javascript
const agent = agentSystem.getCurrentAgent();
const compatibility = mcpIntegration.checkToolCompatibility(agent);

console.log('兼容的工具:', compatibility.compatible);
console.log('不兼容的工具:', compatibility.incompatible);
console.log('缺失的工具:', compatibility.missing);
```

## 🎯 使用方法

### 1. 在Augment Code中选择智能体

```
# 在AI对话框中输入
@ios-developer

# 或者搜索智能体
@代码审查
```

### 2. 编程方式使用

```javascript
// 选择智能体
await agentSystem.selectAgent('ios-developer');

// 生成提示词
const prompt = await agentSystem.generatePrompt('codeAnalysis', {
    fileName: 'Tweak.x',
    codeType: 'objective-c'
});

// 执行任务
const task = await agentSystem.executeAgentTask('debugging', {
    issue: '内存泄漏问题'
});
```

### 3. 智能体工作流程

```javascript
// 定义工作流程
const workflow = [
    { step: 'welcome', context: { projectName: '项目名称' } },
    { step: 'codeAnalysis', context: { fileName: '文件名' } },
    { step: 'debugging', context: { issue: '问题描述' } }
];

// 执行工作流程
for (const { step, context } of workflow) {
    const prompt = await agentSystem.generatePrompt(step, context);
    // 处理提示词...
}
```

## 🔍 故障排除

### 常见问题

#### 1. 智能体加载失败
```
错误: 智能体配置文件格式错误
解决: 检查JSON格式，确保所有必需字段存在
```

#### 2. MCP工具连接失败
```
错误: FuniA0Mcp工具未找到
解决: 确保FuniA0Mcp工具已正确安装和配置
```

#### 3. 提示词模板不存在
```
错误: 提示词模板不存在
解决: 检查提示词文件路径，确保文件存在
```

### 调试模式

启用调试模式获取详细日志：

```json
{
    "logging": {
        "level": "debug",
        "enableDebug": true
    }
}
```

### 日志文件

系统日志保存在：
- 配置日志: `config/augment-agents.log`
- 错误日志: `config/error.log`
- 调试日志: `config/debug.log`

## 📞 技术支持

如果遇到问题，请：

1. 检查系统要求和配置
2. 查看日志文件获取错误信息
3. 参考示例代码和文档
4. 联系技术支持团队

---

**更新日期**: 2024年8月21日  
**版本**: 1.0.0

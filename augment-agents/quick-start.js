#!/usr/bin/env node

/**
 * Augment Code 智能体系统快速启动脚本
 * 提供交互式的系统配置和测试功能
 */

const { getGlobalInstance } = require('./src/index');
const ValidationTest = require('./test/validation-test');
const readline = require('readline');

class QuickStart {
    constructor() {
        this.rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });
        this.agentSystem = null;
    }

    /**
     * 主菜单
     */
    async showMainMenu() {
        console.clear();
        console.log('🤖 Augment Code 智能体系统 - 快速启动');
        console.log('='.repeat(50));
        console.log('1. 初始化系统');
        console.log('2. 运行验证测试');
        console.log('3. 查看智能体列表');
        console.log('4. 测试智能体功能');
        console.log('5. 查看系统状态');
        console.log('6. 运行使用示例');
        console.log('7. 配置向导');
        console.log('0. 退出');
        console.log('='.repeat(50));

        const choice = await this.askQuestion('请选择操作 (0-7): ');
        await this.handleMenuChoice(choice);
    }

    /**
     * 处理菜单选择
     */
    async handleMenuChoice(choice) {
        switch (choice) {
            case '1':
                await this.initializeSystem();
                break;
            case '2':
                await this.runValidationTest();
                break;
            case '3':
                await this.showAgentList();
                break;
            case '4':
                await this.testAgentFunction();
                break;
            case '5':
                await this.showSystemStatus();
                break;
            case '6':
                await this.runExamples();
                break;
            case '7':
                await this.configurationWizard();
                break;
            case '0':
                await this.exit();
                return;
            default:
                console.log('❌ 无效选择，请重试');
                await this.pause();
        }

        await this.showMainMenu();
    }

    /**
     * 初始化系统
     */
    async initializeSystem() {
        console.log('\n🚀 正在初始化智能体系统...');

        try {
            this.agentSystem = getGlobalInstance({
                configPath: './config',
                enableMCP: true,
                autoInit: false
            });

            await this.agentSystem.init();
            console.log('✅ 系统初始化成功！');

            const stats = this.agentSystem.getSystemStats();
            console.log(`📊 加载了 ${stats.agents.total} 个智能体`);
            console.log(`   - 内置智能体: ${stats.agents.builtin}`);
            console.log(`   - 自定义智能体: ${stats.agents.custom}`);

        } catch (error) {
            console.log('❌ 系统初始化失败:', error.message);
        }

        await this.pause();
    }

    /**
     * 运行验证测试
     */
    async runValidationTest() {
        console.log('\n🧪 运行系统验证测试...');

        const validator = new ValidationTest();
        await validator.runAllTests();

        await this.pause();
    }

    /**
     * 显示智能体列表
     */
    async showAgentList() {
        console.log('\n📋 智能体列表:');

        if (!this.agentSystem) {
            console.log('❌ 系统未初始化，请先选择选项1');
            await this.pause();
            return;
        }

        const agents = this.agentSystem.getAgents();
        
        console.log('='.repeat(60));
        agents.forEach((agent, index) => {
            console.log(`${index + 1}. ${agent.icon || '🤖'} ${agent.name} (${agent.id})`);
            console.log(`   类型: ${agent.type} | 分类: ${agent.category}`);
            console.log(`   描述: ${agent.description}`);
            if (agent.tags && agent.tags.length > 0) {
                console.log(`   标签: ${agent.tags.join(', ')}`);
            }
            console.log('');
        });

        await this.pause();
    }

    /**
     * 测试智能体功能
     */
    async testAgentFunction() {
        console.log('\n🎯 测试智能体功能');

        if (!this.agentSystem) {
            console.log('❌ 系统未初始化，请先选择选项1');
            await this.pause();
            return;
        }

        const agents = this.agentSystem.getAgents();
        console.log('\n可用智能体:');
        agents.forEach((agent, index) => {
            console.log(`${index + 1}. ${agent.name} (${agent.id})`);
        });

        const choice = await this.askQuestion('\n请选择智能体编号: ');
        const agentIndex = parseInt(choice) - 1;

        if (agentIndex >= 0 && agentIndex < agents.length) {
            const selectedAgent = agents[agentIndex];
            
            try {
                await this.agentSystem.selectAgent(selectedAgent.id);
                console.log(`✅ 已选择智能体: ${selectedAgent.name}`);

                // 生成欢迎提示词
                const welcomePrompt = await this.agentSystem.generatePrompt('welcome', {
                    userName: '测试用户',
                    projectName: '测试项目'
                });

                console.log('\n📝 生成的欢迎提示词:');
                console.log('-'.repeat(40));
                console.log(welcomePrompt.substring(0, 500) + '...');
                console.log('-'.repeat(40));

                // 如果智能体支持MCP工具，测试工具调用
                if (selectedAgent.mcpTools && selectedAgent.mcpTools.includes('FuniA0Mcp')) {
                    console.log('\n🔧 测试MCP工具调用...');
                    
                    const toolResult = await this.agentSystem.callMCPTool('FuniA0Mcp', 'technical-proposal', {
                        projectType: '测试项目',
                        requirements: '测试需求'
                    });

                    console.log('✅ MCP工具调用成功:', toolResult.success);
                }

            } catch (error) {
                console.log('❌ 智能体测试失败:', error.message);
            }
        } else {
            console.log('❌ 无效选择');
        }

        await this.pause();
    }

    /**
     * 显示系统状态
     */
    async showSystemStatus() {
        console.log('\n📊 系统状态信息');

        if (!this.agentSystem) {
            console.log('❌ 系统未初始化');
            await this.pause();
            return;
        }

        const stats = this.agentSystem.getSystemStats();
        const currentAgent = this.agentSystem.getCurrentAgent();

        console.log('='.repeat(50));
        console.log('🤖 智能体统计:');
        console.log(`   总数: ${stats.agents.total}`);
        console.log(`   内置: ${stats.agents.builtin}`);
        console.log(`   自定义: ${stats.agents.custom}`);
        console.log(`   分类分布: ${JSON.stringify(stats.agents.categories, null, 2)}`);

        console.log('\n💻 系统信息:');
        console.log(`   运行时间: ${Math.floor(stats.system.uptime)} 秒`);
        console.log(`   内存使用: ${Math.floor(stats.system.memory.used / 1024 / 1024)} MB`);
        console.log(`   初始化状态: ${stats.system.initialized ? '✅ 已初始化' : '❌ 未初始化'}`);

        if (currentAgent) {
            console.log('\n🎯 当前智能体:');
            console.log(`   名称: ${currentAgent.name}`);
            console.log(`   ID: ${currentAgent.id}`);
            console.log(`   类型: ${currentAgent.type}`);
            console.log(`   版本: ${currentAgent.version}`);
            if (currentAgent.mcpTools) {
                console.log(`   MCP工具: ${currentAgent.mcpTools.join(', ')}`);
            }
        } else {
            console.log('\n🎯 当前智能体: 未选择');
        }

        if (stats.mcp) {
            console.log('\n🔧 MCP工具状态:');
            console.log(`   总调用次数: ${stats.mcp.totalCalls}`);
            console.log(`   成功率: ${stats.mcp.successRate}%`);
        }

        await this.pause();
    }

    /**
     * 运行使用示例
     */
    async runExamples() {
        console.log('\n📚 运行使用示例...');

        try {
            const examples = require('./examples/usage-examples');
            await examples.runAllExamples();
        } catch (error) {
            console.log('❌ 示例运行失败:', error.message);
        }

        await this.pause();
    }

    /**
     * 配置向导
     */
    async configurationWizard() {
        console.log('\n⚙️  配置向导');
        console.log('此功能将帮助您配置智能体系统');

        const enableMCP = await this.askQuestion('是否启用MCP集成? (y/n): ');
        const defaultAgent = await this.askQuestion('默认智能体ID (ios-developer): ') || 'ios-developer';
        const language = await this.askQuestion('系统语言 (zh-CN): ') || 'zh-CN';

        console.log('\n📝 配置摘要:');
        console.log(`   MCP集成: ${enableMCP.toLowerCase() === 'y' ? '启用' : '禁用'}`);
        console.log(`   默认智能体: ${defaultAgent}`);
        console.log(`   系统语言: ${language}`);

        const confirm = await this.askQuestion('\n确认应用配置? (y/n): ');
        
        if (confirm.toLowerCase() === 'y') {
            console.log('✅ 配置已保存（注意：这是演示，实际配置需要修改config/settings.json）');
        } else {
            console.log('❌ 配置已取消');
        }

        await this.pause();
    }

    /**
     * 询问用户输入
     */
    askQuestion(question) {
        return new Promise((resolve) => {
            this.rl.question(question, (answer) => {
                resolve(answer.trim());
            });
        });
    }

    /**
     * 暂停等待用户按键
     */
    async pause() {
        await this.askQuestion('\n按 Enter 键继续...');
    }

    /**
     * 退出程序
     */
    async exit() {
        console.log('\n👋 感谢使用 Augment Code 智能体系统！');
        
        if (this.agentSystem) {
            await this.agentSystem.destroy();
        }
        
        this.rl.close();
        process.exit(0);
    }

    /**
     * 启动快速启动程序
     */
    async start() {
        console.log('🚀 启动 Augment Code 智能体系统快速启动程序...\n');
        await this.showMainMenu();
    }
}

// 如果直接运行此文件，启动快速启动程序
if (require.main === module) {
    const quickStart = new QuickStart();
    quickStart.start().catch(console.error);
}

module.exports = QuickStart;

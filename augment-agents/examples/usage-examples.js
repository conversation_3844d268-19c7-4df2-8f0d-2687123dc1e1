/**
 * Augment Code 智能体系统使用示例
 * 演示如何在Augment Code中使用智能体功能
 */

const { AugmentAgentSystem, getGlobalInstance } = require('../src/index');

/**
 * 示例1: 基本使用流程
 */
async function basicUsageExample() {
    console.log('=== 基本使用示例 ===');
    
    try {
        // 获取全局实例
        const agentSystem = getGlobalInstance();
        
        // 等待初始化完成
        if (!agentSystem.isInitialized) {
            await agentSystem.init();
        }

        // 获取所有可用智能体
        const agents = agentSystem.getAgents();
        console.log('可用智能体:', agents.map(a => `${a.name} (${a.id})`));

        // 选择iOS开发专家
        const iosAgent = await agentSystem.selectAgent('ios-developer');
        console.log('已选择智能体:', iosAgent.name);

        // 生成欢迎提示词
        const welcomePrompt = await agentSystem.generatePrompt('welcome', {
            userName: '开发者',
            projectName: 'iOS虚拟摄像头'
        });
        console.log('欢迎提示词:', welcomePrompt.substring(0, 200) + '...');

        // 执行代码分析任务
        const analysisTask = await agentSystem.executeAgentTask('codeAnalysis', {
            fileName: 'Tweak.x',
            codeType: 'objective-c',
            focus: 'CMSampleBuffer处理'
        });
        console.log('分析任务已准备:', analysisTask.taskType);

    } catch (error) {
        console.error('基本使用示例失败:', error);
    }
}

/**
 * 示例2: 智能体搜索和选择
 */
async function agentSearchExample() {
    console.log('\n=== 智能体搜索示例 ===');
    
    try {
        const agentSystem = getGlobalInstance();

        // 搜索iOS相关智能体
        const iosAgents = agentSystem.searchAgents('iOS');
        console.log('iOS相关智能体:', iosAgents.map(a => `${a.name} (评分: ${a.score})`));

        // 搜索代码审查相关智能体
        const reviewAgents = agentSystem.searchAgents('代码审查');
        console.log('代码审查智能体:', reviewAgents.map(a => `${a.name} (评分: ${a.score})`));

        // 按分类获取智能体
        const allAgents = agentSystem.getAgents();
        const categories = {};
        allAgents.forEach(agent => {
            const category = agent.category || 'other';
            if (!categories[category]) {
                categories[category] = [];
            }
            categories[category].push(agent.name);
        });
        console.log('按分类分组:', categories);

    } catch (error) {
        console.error('智能体搜索示例失败:', error);
    }
}

/**
 * 示例3: MCP工具集成
 */
async function mcpIntegrationExample() {
    console.log('\n=== MCP工具集成示例 ===');
    
    try {
        const agentSystem = getGlobalInstance();

        // 选择项目经理智能体
        const pmAgent = await agentSystem.selectAgent('project-manager');
        console.log('已选择项目经理智能体');

        // 调用FuniA0Mcp工具生成技术方案
        const proposalResult = await agentSystem.callMCPTool('FuniA0Mcp', 'technical-proposal', {
            projectType: 'iOS虚拟摄像头',
            requirements: '实现摄像头画面替换功能',
            technologies: ['Objective-C', 'AVFoundation', 'CoreMedia']
        });
        console.log('技术方案生成结果:', proposalResult);

        // 调用产品经理自动配置
        const configResult = await agentSystem.callMCPTool('FuniA0Mcp', 'pm-auto-config', {
            action: 'set-prd',
            path: './docs/PRD.md'
        });
        console.log('产品配置结果:', configResult);

    } catch (error) {
        console.error('MCP工具集成示例失败:', error);
    }
}

/**
 * 示例4: 自定义智能体创建
 */
async function customAgentExample() {
    console.log('\n=== 自定义智能体示例 ===');
    
    try {
        const agentSystem = getGlobalInstance();

        // 创建自定义智能体配置
        const customAgentConfig = {
            id: 'my-ios-tester',
            name: 'iOS测试专家',
            description: '专注于iOS应用测试的智能助手，擅长单元测试、UI测试和性能测试',
            category: 'testing',
            icon: '🧪',
            tags: ['iOS', '测试', 'XCTest', 'UI测试'],
            
            prompts: {
                system: 'prompts/zh-CN/testing/system.md',
                welcome: 'prompts/zh-CN/testing/welcome.md'
            },
            
            mcpTools: ['FuniA0Mcp', 'file-manager', 'terminal'],
            
            capabilities: [
                'unit-testing',
                'ui-testing',
                'performance-testing',
                'test-automation'
            ],
            
            settings: {
                autoRun: false,
                maxTokens: 3000,
                temperature: 0.5
            }
        };

        // 创建自定义智能体
        const customAgent = await agentSystem.createCustomAgent(customAgentConfig);
        console.log('创建自定义智能体:', customAgent.name);

        // 选择并使用自定义智能体
        await agentSystem.selectAgent(customAgent.id);
        console.log('已选择自定义智能体');

        // 导出智能体配置
        const exportedConfig = agentSystem.exportAgentConfig(customAgent.id);
        console.log('导出的配置:', Object.keys(exportedConfig));

    } catch (error) {
        console.error('自定义智能体示例失败:', error);
    }
}

/**
 * 示例5: 智能体工作流程
 */
async function workflowExample() {
    console.log('\n=== 智能体工作流程示例 ===');
    
    try {
        const agentSystem = getGlobalInstance();

        // 选择iOS开发专家
        await agentSystem.selectAgent('ios-developer');

        // 模拟完整的开发工作流程
        const workflowSteps = [
            { type: 'welcome', context: { projectName: 'iOS虚拟摄像头' } },
            { type: 'codeAnalysis', context: { fileName: 'Tweak.x' } },
            { type: 'debugging', context: { issue: '内存泄漏' } },
            { type: 'system', context: { task: '性能优化' } }
        ];

        for (const step of workflowSteps) {
            console.log(`执行工作流步骤: ${step.type}`);
            
            const prompt = await agentSystem.generatePrompt(step.type, step.context);
            console.log(`生成提示词长度: ${prompt.length} 字符`);
            
            // 这里可以将提示词发送给AI模型处理
            // const aiResponse = await sendToAI(prompt);
        }

    } catch (error) {
        console.error('工作流程示例失败:', error);
    }
}

/**
 * 示例6: 系统监控和统计
 */
async function monitoringExample() {
    console.log('\n=== 系统监控示例 ===');
    
    try {
        const agentSystem = getGlobalInstance();

        // 获取系统统计信息
        const stats = agentSystem.getSystemStats();
        console.log('系统统计:', {
            智能体总数: stats.agents.total,
            内置智能体: stats.agents.builtin,
            自定义智能体: stats.agents.custom,
            分类分布: stats.agents.categories,
            系统运行时间: `${Math.floor(stats.system.uptime)}秒`,
            内存使用: `${Math.floor(stats.system.memory.used / 1024 / 1024)}MB`
        });

        // 获取当前智能体信息
        const currentAgent = agentSystem.getCurrentAgent();
        if (currentAgent) {
            console.log('当前智能体:', {
                名称: currentAgent.name,
                类型: currentAgent.type,
                版本: currentAgent.version,
                工具数量: currentAgent.mcpTools ? currentAgent.mcpTools.length : 0
            });
        }

    } catch (error) {
        console.error('系统监控示例失败:', error);
    }
}

/**
 * 运行所有示例
 */
async function runAllExamples() {
    console.log('开始运行Augment智能体系统示例...\n');
    
    await basicUsageExample();
    await agentSearchExample();
    await mcpIntegrationExample();
    await customAgentExample();
    await workflowExample();
    await monitoringExample();
    
    console.log('\n所有示例运行完成！');
}

// 如果直接运行此文件，执行所有示例
if (require.main === module) {
    runAllExamples().catch(console.error);
}

module.exports = {
    basicUsageExample,
    agentSearchExample,
    mcpIntegrationExample,
    customAgentExample,
    workflowExample,
    monitoringExample,
    runAllExamples
};

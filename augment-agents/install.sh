#!/bin/bash

# Augment Code 智能体系统安装脚本
# 用于快速安装和配置智能体系统

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}${message}${NC}"
}

print_success() {
    print_message $GREEN "✅ $1"
}

print_error() {
    print_message $RED "❌ $1"
}

print_warning() {
    print_message $YELLOW "⚠️  $1"
}

print_info() {
    print_message $BLUE "ℹ️  $1"
}

# 检查系统要求
check_requirements() {
    print_info "检查系统要求..."

    # 检查Node.js
    if command -v node >/dev/null 2>&1; then
        NODE_VERSION=$(node --version)
        print_success "Node.js 已安装: $NODE_VERSION"
    else
        print_error "Node.js 未安装，请先安装 Node.js 14.0 或更高版本"
        exit 1
    fi

    # 检查npm
    if command -v npm >/dev/null 2>&1; then
        NPM_VERSION=$(npm --version)
        print_success "npm 已安装: $NPM_VERSION"
    else
        print_error "npm 未安装"
        exit 1
    fi

    # 检查Git（可选）
    if command -v git >/dev/null 2>&1; then
        GIT_VERSION=$(git --version)
        print_success "Git 已安装: $GIT_VERSION"
    else
        print_warning "Git 未安装，某些功能可能受限"
    fi
}

# 安装依赖
install_dependencies() {
    print_info "安装项目依赖..."

    if [ -f "package.json" ]; then
        npm install
        print_success "依赖安装完成"
    else
        print_error "package.json 文件不存在"
        exit 1
    fi
}

# 创建必要的目录
create_directories() {
    print_info "创建必要的目录结构..."

    # 确保所有必要目录存在
    mkdir -p config/agents/custom
    mkdir -p config/prompts/templates
    mkdir -p logs
    mkdir -p temp

    print_success "目录结构创建完成"
}

# 设置权限
set_permissions() {
    print_info "设置文件权限..."

    # 设置脚本执行权限
    chmod +x quick-start.js
    chmod +x install.sh

    # 设置配置文件权限
    chmod 644 config/*.json 2>/dev/null || true
    chmod 644 config/agents/*/*.json 2>/dev/null || true

    print_success "权限设置完成"
}

# 验证安装
validate_installation() {
    print_info "验证安装..."

    # 运行验证测试
    if node test/validation-test.js >/dev/null 2>&1; then
        print_success "安装验证通过"
    else
        print_warning "安装验证失败，但系统可能仍然可用"
    fi
}

# 显示安装后信息
show_post_install_info() {
    echo ""
    print_success "🎉 Augment Code 智能体系统安装完成！"
    echo ""
    print_info "快速开始:"
    echo "  1. 运行快速启动程序: node quick-start.js"
    echo "  2. 运行验证测试: npm test"
    echo "  3. 查看使用示例: npm run example"
    echo ""
    print_info "配置文件位置:"
    echo "  - 全局设置: config/settings.json"
    echo "  - 智能体配置: config/agents/"
    echo "  - 提示词模板: config/prompts/"
    echo ""
    print_info "文档位置:"
    echo "  - 配置指南: docs/configuration-guide.md"
    echo "  - 使用示例: examples/usage-examples.js"
    echo ""
    print_info "如需帮助，请查看 README.md 或运行 node quick-start.js"
}

# 主安装流程
main() {
    echo "🤖 Augment Code 智能体系统安装程序"
    echo "========================================"
    echo ""

    # 检查是否在正确的目录
    if [ ! -f "package.json" ] || [ ! -d "src" ]; then
        print_error "请在 augment-agents 项目根目录下运行此脚本"
        exit 1
    fi

    # 执行安装步骤
    check_requirements
    echo ""
    
    install_dependencies
    echo ""
    
    create_directories
    echo ""
    
    set_permissions
    echo ""
    
    validate_installation
    echo ""
    
    show_post_install_info
}

# 错误处理
trap 'print_error "安装过程中发生错误，请检查上面的错误信息"; exit 1' ERR

# 运行主程序
main "$@"

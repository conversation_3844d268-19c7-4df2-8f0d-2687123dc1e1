# Augment Code 智能体系统

基于 Trae IDE 智能体设计理念，为 Augment Code 实现的智能体配置和管理系统。

## 功能特性

- 🤖 **智能体配置**：支持内置和自定义智能体
- 🌏 **中文支持**：完整的中文提示词模板
- 🔧 **MCP集成**：与现有FuniA0Mcp工具完全兼容
- 🎨 **可视化管理**：直观的智能体选择和管理界面
- 📝 **提示词引擎**：支持变量替换和上下文注入

## 目录结构

```
augment-agents/
├── config/                    # 配置文件
│   ├── agents/               # 智能体配置
│   │   ├── builtin/         # 内置智能体
│   │   └── custom/          # 自定义智能体
│   ├── prompts/             # 提示词模板
│   │   ├── zh-CN/          # 中文提示词
│   │   └── templates/      # 模板文件
│   └── settings.json       # 全局设置
├── src/                     # 源代码
│   ├── agent-manager.js    # 智能体管理器
│   ├── prompt-engine.js    # 提示词引擎
│   ├── ui-components/      # UI组件
│   └── mcp-integration.js  # MCP集成
└── README.md
```

## 快速开始

1. 配置智能体：编辑 `config/agents/` 目录下的配置文件
2. 自定义提示词：修改 `config/prompts/zh-CN/` 目录下的模板
3. 在 Augment Code 中使用 `@智能体名称` 选择智能体

## 兼容性

- ✅ 与现有 FuniA0Mcp 工具完全兼容
- ✅ 支持 Augment Code 现有 MCP 配置
- ✅ 向后兼容现有工作流程

## 智能体类型

### 内置智能体
- **iOS开发专家**：专注于iOS应用开发，精通Objective-C、Swift
- **代码审查员**：专业的代码质量分析和改进建议
- **项目经理**：项目规划、任务分解和进度管理

### 自定义智能体
支持用户根据特定需求创建个性化智能体，包括：
- 自定义提示词模板
- 特定工具集配置
- 个性化参数设置

## 使用方法

### 1. 选择智能体
在 Augment Code 对话框中输入 `@` 触发智能体选择器，选择所需的智能体。

### 2. 智能体管理
通过 Augment Code 设置面板 > 智能体 进行管理：
- 创建新智能体
- 编辑现有配置
- 导入/导出智能体

### 3. 提示词自定义
编辑 `config/prompts/zh-CN/` 目录下的模板文件，支持变量替换和上下文注入。

## 配置示例

详细的配置示例和模板请参考 `config/` 目录下的文件。

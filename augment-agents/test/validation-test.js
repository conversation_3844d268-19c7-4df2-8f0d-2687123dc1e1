/**
 * Augment Code 智能体系统验证测试
 * 用于验证系统功能的正确性和完整性
 */

const { AugmentAgentSystem } = require('../src/index');
const fs = require('fs');
const path = require('path');

class ValidationTest {
    constructor() {
        this.agentSystem = null;
        this.testResults = [];
        this.configPath = path.join(__dirname, '../config');
    }

    /**
     * 运行所有验证测试
     */
    async runAllTests() {
        console.log('🧪 开始运行Augment智能体系统验证测试...\n');

        try {
            await this.testSystemInitialization();
            await this.testAgentLoading();
            await this.testAgentSelection();
            await this.testPromptGeneration();
            await this.testMCPIntegration();
            await this.testCustomAgentCreation();
            await this.testConfigurationValidation();
            await this.testErrorHandling();

            this.printTestResults();
        } catch (error) {
            console.error('❌ 测试运行失败:', error);
        } finally {
            if (this.agentSystem) {
                await this.agentSystem.destroy();
            }
        }
    }

    /**
     * 测试系统初始化
     */
    async testSystemInitialization() {
        console.log('📋 测试系统初始化...');

        try {
            this.agentSystem = new AugmentAgentSystem({
                configPath: this.configPath,
                enableMCP: true,
                autoInit: false
            });

            await this.agentSystem.init();

            this.addTestResult('系统初始化', true, '系统成功初始化');
            console.log('✅ 系统初始化测试通过');
        } catch (error) {
            this.addTestResult('系统初始化', false, error.message);
            console.log('❌ 系统初始化测试失败:', error.message);
        }
    }

    /**
     * 测试智能体加载
     */
    async testAgentLoading() {
        console.log('📋 测试智能体加载...');

        try {
            const agents = this.agentSystem.getAgents();
            
            // 检查是否加载了内置智能体
            const expectedAgents = ['ios-developer', 'code-reviewer', 'project-manager'];
            const loadedAgentIds = agents.map(a => a.id);
            
            let allLoaded = true;
            const missingAgents = [];
            
            for (const expectedId of expectedAgents) {
                if (!loadedAgentIds.includes(expectedId)) {
                    allLoaded = false;
                    missingAgents.push(expectedId);
                }
            }

            if (allLoaded) {
                this.addTestResult('智能体加载', true, `成功加载 ${agents.length} 个智能体`);
                console.log('✅ 智能体加载测试通过');
            } else {
                this.addTestResult('智能体加载', false, `缺少智能体: ${missingAgents.join(', ')}`);
                console.log('❌ 智能体加载测试失败');
            }
        } catch (error) {
            this.addTestResult('智能体加载', false, error.message);
            console.log('❌ 智能体加载测试失败:', error.message);
        }
    }

    /**
     * 测试智能体选择
     */
    async testAgentSelection() {
        console.log('📋 测试智能体选择...');

        try {
            // 选择iOS开发专家
            const agent = await this.agentSystem.selectAgent('ios-developer');
            
            if (agent && agent.id === 'ios-developer') {
                this.addTestResult('智能体选择', true, `成功选择智能体: ${agent.name}`);
                console.log('✅ 智能体选择测试通过');
            } else {
                this.addTestResult('智能体选择', false, '智能体选择失败');
                console.log('❌ 智能体选择测试失败');
            }

            // 测试无效智能体选择
            try {
                await this.agentSystem.selectAgent('non-existent-agent');
                this.addTestResult('无效智能体处理', false, '应该抛出错误但没有');
            } catch (error) {
                this.addTestResult('无效智能体处理', true, '正确处理无效智能体');
            }
        } catch (error) {
            this.addTestResult('智能体选择', false, error.message);
            console.log('❌ 智能体选择测试失败:', error.message);
        }
    }

    /**
     * 测试提示词生成
     */
    async testPromptGeneration() {
        console.log('📋 测试提示词生成...');

        try {
            // 确保已选择智能体
            await this.agentSystem.selectAgent('ios-developer');

            // 测试系统提示词生成
            const systemPrompt = await this.agentSystem.generatePrompt('system', {
                projectName: '测试项目',
                userName: '测试用户'
            });

            if (systemPrompt && systemPrompt.length > 0) {
                this.addTestResult('提示词生成', true, `成功生成提示词 (${systemPrompt.length} 字符)`);
                console.log('✅ 提示词生成测试通过');
            } else {
                this.addTestResult('提示词生成', false, '提示词为空');
                console.log('❌ 提示词生成测试失败');
            }

            // 测试变量替换
            const welcomePrompt = await this.agentSystem.generatePrompt('welcome', {
                userName: '测试用户',
                projectName: '测试项目'
            });

            if (welcomePrompt.includes('测试用户') || welcomePrompt.includes('测试项目')) {
                this.addTestResult('变量替换', true, '变量替换正常工作');
            } else {
                this.addTestResult('变量替换', false, '变量替换未生效');
            }
        } catch (error) {
            this.addTestResult('提示词生成', false, error.message);
            console.log('❌ 提示词生成测试失败:', error.message);
        }
    }

    /**
     * 测试MCP集成
     */
    async testMCPIntegration() {
        console.log('📋 测试MCP集成...');

        try {
            if (!this.agentSystem.mcpIntegration) {
                this.addTestResult('MCP集成', false, 'MCP集成未启用');
                return;
            }

            // 测试FuniA0Mcp工具调用
            const result = await this.agentSystem.callMCPTool('FuniA0Mcp', 'technical-proposal', {
                projectType: '测试项目',
                requirements: '测试需求'
            });

            if (result && result.success) {
                this.addTestResult('MCP工具调用', true, 'FuniA0Mcp工具调用成功');
                console.log('✅ MCP集成测试通过');
            } else {
                this.addTestResult('MCP工具调用', false, 'FuniA0Mcp工具调用失败');
                console.log('❌ MCP集成测试失败');
            }

            // 测试工具兼容性检查
            const currentAgent = this.agentSystem.getCurrentAgent();
            if (currentAgent) {
                const compatibility = this.agentSystem.mcpIntegration.checkToolCompatibility(currentAgent);
                this.addTestResult('工具兼容性检查', true, `兼容工具: ${compatibility.compatible.length}`);
            }
        } catch (error) {
            this.addTestResult('MCP集成', false, error.message);
            console.log('❌ MCP集成测试失败:', error.message);
        }
    }

    /**
     * 测试自定义智能体创建
     */
    async testCustomAgentCreation() {
        console.log('📋 测试自定义智能体创建...');

        try {
            const customAgentConfig = {
                id: 'test-custom-agent',
                name: '测试自定义智能体',
                description: '用于测试的自定义智能体',
                category: 'testing',
                version: '1.0.0',
                mcpTools: ['FuniA0Mcp'],
                capabilities: ['testing']
            };

            // 创建自定义智能体
            const customAgent = await this.agentSystem.createCustomAgent(customAgentConfig);

            if (customAgent && customAgent.id === 'test-custom-agent') {
                this.addTestResult('自定义智能体创建', true, '成功创建自定义智能体');
                console.log('✅ 自定义智能体创建测试通过');

                // 测试选择自定义智能体
                await this.agentSystem.selectAgent('test-custom-agent');
                this.addTestResult('自定义智能体选择', true, '成功选择自定义智能体');

                // 清理：删除测试智能体
                await this.agentSystem.deleteCustomAgent('test-custom-agent');
                this.addTestResult('自定义智能体删除', true, '成功删除自定义智能体');
            } else {
                this.addTestResult('自定义智能体创建', false, '自定义智能体创建失败');
                console.log('❌ 自定义智能体创建测试失败');
            }
        } catch (error) {
            this.addTestResult('自定义智能体创建', false, error.message);
            console.log('❌ 自定义智能体创建测试失败:', error.message);
        }
    }

    /**
     * 测试配置验证
     */
    async testConfigurationValidation() {
        console.log('📋 测试配置验证...');

        try {
            // 检查配置文件是否存在
            const settingsPath = path.join(this.configPath, 'settings.json');
            const settingsExist = fs.existsSync(settingsPath);

            if (settingsExist) {
                this.addTestResult('配置文件存在', true, 'settings.json 文件存在');
            } else {
                this.addTestResult('配置文件存在', false, 'settings.json 文件不存在');
            }

            // 检查智能体配置文件
            const agentsPath = path.join(this.configPath, 'agents', 'builtin');
            const agentFiles = fs.readdirSync(agentsPath).filter(f => f.endsWith('.json'));

            if (agentFiles.length >= 3) {
                this.addTestResult('智能体配置文件', true, `找到 ${agentFiles.length} 个智能体配置文件`);
            } else {
                this.addTestResult('智能体配置文件', false, '智能体配置文件不足');
            }

            // 检查提示词文件
            const promptsPath = path.join(this.configPath, 'prompts', 'zh-CN');
            const promptDirs = fs.readdirSync(promptsPath, { withFileTypes: true })
                .filter(dirent => dirent.isDirectory())
                .map(dirent => dirent.name);

            if (promptDirs.length >= 2) {
                this.addTestResult('提示词文件', true, `找到 ${promptDirs.length} 个提示词目录`);
            } else {
                this.addTestResult('提示词文件', false, '提示词文件不足');
            }

            console.log('✅ 配置验证测试通过');
        } catch (error) {
            this.addTestResult('配置验证', false, error.message);
            console.log('❌ 配置验证测试失败:', error.message);
        }
    }

    /**
     * 测试错误处理
     */
    async testErrorHandling() {
        console.log('📋 测试错误处理...');

        try {
            // 测试无效配置处理
            try {
                await this.agentSystem.createCustomAgent({
                    // 缺少必需字段
                    name: '无效智能体'
                });
                this.addTestResult('无效配置处理', false, '应该抛出错误但没有');
            } catch (error) {
                this.addTestResult('无效配置处理', true, '正确处理无效配置');
            }

            // 测试重复ID处理
            try {
                await this.agentSystem.createCustomAgent({
                    id: 'ios-developer', // 重复的ID
                    name: '重复智能体',
                    description: '测试重复ID'
                });
                this.addTestResult('重复ID处理', false, '应该抛出错误但没有');
            } catch (error) {
                this.addTestResult('重复ID处理', true, '正确处理重复ID');
            }

            console.log('✅ 错误处理测试通过');
        } catch (error) {
            this.addTestResult('错误处理', false, error.message);
            console.log('❌ 错误处理测试失败:', error.message);
        }
    }

    /**
     * 添加测试结果
     */
    addTestResult(testName, passed, message) {
        this.testResults.push({
            name: testName,
            passed,
            message,
            timestamp: new Date().toISOString()
        });
    }

    /**
     * 打印测试结果
     */
    printTestResults() {
        console.log('\n📊 测试结果汇总:');
        console.log('='.repeat(60));

        let passedCount = 0;
        let totalCount = this.testResults.length;

        for (const result of this.testResults) {
            const status = result.passed ? '✅ 通过' : '❌ 失败';
            console.log(`${status} ${result.name}: ${result.message}`);
            
            if (result.passed) {
                passedCount++;
            }
        }

        console.log('='.repeat(60));
        console.log(`总计: ${totalCount} 个测试, ${passedCount} 个通过, ${totalCount - passedCount} 个失败`);
        console.log(`成功率: ${Math.round((passedCount / totalCount) * 100)}%`);

        if (passedCount === totalCount) {
            console.log('\n🎉 所有测试通过！系统运行正常。');
        } else {
            console.log('\n⚠️  部分测试失败，请检查相关配置和实现。');
        }
    }
}

// 如果直接运行此文件，执行验证测试
if (require.main === module) {
    const validator = new ValidationTest();
    validator.runAllTests().catch(console.error);
}

module.exports = ValidationTest;

/**
 * Augment Code 智能体管理器
 * 负责智能体的加载、管理和执行
 */

const fs = require('fs');
const path = require('path');
const EventEmitter = require('events');

class AgentManager extends EventEmitter {
    constructor(configPath = './config') {
        super();
        this.configPath = configPath;
        this.agents = new Map();
        this.currentAgent = null;
        this.settings = null;
        this.promptEngine = null;
        
        this.init();
    }

    /**
     * 初始化智能体管理器
     */
    async init() {
        try {
            await this.loadSettings();
            await this.loadAgents();
            await this.initPromptEngine();
            
            this.emit('initialized');
            console.log('智能体管理器初始化完成');
        } catch (error) {
            console.error('智能体管理器初始化失败:', error);
            this.emit('error', error);
        }
    }

    /**
     * 加载全局设置
     */
    async loadSettings() {
        const settingsPath = path.join(this.configPath, 'settings.json');
        
        if (fs.existsSync(settingsPath)) {
            const settingsData = fs.readFileSync(settingsPath, 'utf8');
            this.settings = JSON.parse(settingsData);
        } else {
            // 使用默认设置
            this.settings = this.getDefaultSettings();
            await this.saveSettings();
        }
    }

    /**
     * 获取默认设置
     */
    getDefaultSettings() {
        return {
            version: "1.0.0",
            language: "zh-CN",
            agents: {
                defaultAgent: "ios-developer",
                autoLoad: true,
                enableCustomAgents: true
            },
            prompts: {
                templateEngine: "mustache",
                variablePrefix: "{{",
                variableSuffix: "}}",
                enableContextInjection: true
            },
            mcp: {
                enableIntegration: true,
                compatibilityMode: true,
                preserveExistingTools: true,
                funiA0McpIntegration: {
                    enabled: true,
                    priority: "high"
                }
            }
        };
    }

    /**
     * 保存设置
     */
    async saveSettings() {
        const settingsPath = path.join(this.configPath, 'settings.json');
        fs.writeFileSync(settingsPath, JSON.stringify(this.settings, null, 2));
    }

    /**
     * 加载所有智能体
     */
    async loadAgents() {
        const agentsPath = path.join(this.configPath, 'agents');
        
        // 加载内置智能体
        await this.loadAgentsFromDirectory(path.join(agentsPath, 'builtin'), 'builtin');
        
        // 加载自定义智能体
        if (this.settings.agents.enableCustomAgents) {
            await this.loadAgentsFromDirectory(path.join(agentsPath, 'custom'), 'custom');
        }

        console.log(`已加载 ${this.agents.size} 个智能体`);
    }

    /**
     * 从指定目录加载智能体
     */
    async loadAgentsFromDirectory(dirPath, type) {
        if (!fs.existsSync(dirPath)) {
            fs.mkdirSync(dirPath, { recursive: true });
            return;
        }

        const files = fs.readdirSync(dirPath);
        
        for (const file of files) {
            if (path.extname(file) === '.json') {
                try {
                    const agentPath = path.join(dirPath, file);
                    const agentData = JSON.parse(fs.readFileSync(agentPath, 'utf8'));
                    
                    agentData.type = type;
                    agentData.configPath = agentPath;
                    
                    this.agents.set(agentData.id, agentData);
                    console.log(`加载智能体: ${agentData.name} (${agentData.id})`);
                } catch (error) {
                    console.error(`加载智能体失败 ${file}:`, error);
                }
            }
        }
    }

    /**
     * 初始化提示词引擎
     */
    async initPromptEngine() {
        const PromptEngine = require('./prompt-engine');
        this.promptEngine = new PromptEngine(this.configPath, this.settings);
        await this.promptEngine.init();
    }

    /**
     * 获取所有智能体列表
     */
    getAgents() {
        return Array.from(this.agents.values()).map(agent => ({
            id: agent.id,
            name: agent.name,
            description: agent.description,
            category: agent.category,
            icon: agent.icon,
            type: agent.type,
            tags: agent.tags || []
        }));
    }

    /**
     * 根据ID获取智能体
     */
    getAgent(agentId) {
        return this.agents.get(agentId);
    }

    /**
     * 搜索智能体
     */
    searchAgents(query) {
        const results = [];
        const lowerQuery = query.toLowerCase();

        for (const agent of this.agents.values()) {
            const score = this.calculateSearchScore(agent, lowerQuery);
            if (score > 0) {
                results.push({ ...agent, score });
            }
        }

        return results.sort((a, b) => b.score - a.score);
    }

    /**
     * 计算搜索匹配分数
     */
    calculateSearchScore(agent, query) {
        let score = 0;

        // 名称匹配
        if (agent.name.toLowerCase().includes(query)) {
            score += 10;
        }

        // 描述匹配
        if (agent.description.toLowerCase().includes(query)) {
            score += 5;
        }

        // 标签匹配
        if (agent.tags) {
            for (const tag of agent.tags) {
                if (tag.toLowerCase().includes(query)) {
                    score += 3;
                }
            }
        }

        // 分类匹配
        if (agent.category && agent.category.toLowerCase().includes(query)) {
            score += 2;
        }

        return score;
    }

    /**
     * 选择智能体
     */
    async selectAgent(agentId) {
        const agent = this.agents.get(agentId);
        if (!agent) {
            throw new Error(`智能体不存在: ${agentId}`);
        }

        this.currentAgent = agent;
        
        // 加载智能体的提示词
        await this.loadAgentPrompts(agent);
        
        this.emit('agentSelected', agent);
        console.log(`已选择智能体: ${agent.name}`);
        
        return agent;
    }

    /**
     * 加载智能体提示词
     */
    async loadAgentPrompts(agent) {
        if (agent.prompts) {
            for (const [key, promptPath] of Object.entries(agent.prompts)) {
                try {
                    const fullPath = path.join(this.configPath, promptPath);
                    if (fs.existsSync(fullPath)) {
                        const promptContent = fs.readFileSync(fullPath, 'utf8');
                        agent.loadedPrompts = agent.loadedPrompts || {};
                        agent.loadedPrompts[key] = promptContent;
                    }
                } catch (error) {
                    console.error(`加载提示词失败 ${promptPath}:`, error);
                }
            }
        }
    }

    /**
     * 获取当前智能体
     */
    getCurrentAgent() {
        return this.currentAgent;
    }

    /**
     * 生成智能体提示词
     */
    async generatePrompt(promptType, context = {}) {
        if (!this.currentAgent) {
            throw new Error('未选择智能体');
        }

        return await this.promptEngine.generatePrompt(
            this.currentAgent,
            promptType,
            context
        );
    }

    /**
     * 创建自定义智能体
     */
    async createCustomAgent(agentConfig) {
        // 验证配置
        this.validateAgentConfig(agentConfig);
        
        // 设置默认值
        agentConfig.type = 'custom';
        agentConfig.version = agentConfig.version || '1.0.0';
        agentConfig.language = agentConfig.language || this.settings.language;
        
        // 保存到文件
        const customPath = path.join(this.configPath, 'agents', 'custom');
        if (!fs.existsSync(customPath)) {
            fs.mkdirSync(customPath, { recursive: true });
        }
        
        const filePath = path.join(customPath, `${agentConfig.id}.json`);
        fs.writeFileSync(filePath, JSON.stringify(agentConfig, null, 2));
        
        // 添加到内存
        agentConfig.configPath = filePath;
        this.agents.set(agentConfig.id, agentConfig);
        
        this.emit('agentCreated', agentConfig);
        console.log(`创建自定义智能体: ${agentConfig.name}`);
        
        return agentConfig;
    }

    /**
     * 验证智能体配置
     */
    validateAgentConfig(config) {
        const required = ['id', 'name', 'description'];
        for (const field of required) {
            if (!config[field]) {
                throw new Error(`缺少必需字段: ${field}`);
            }
        }

        if (this.agents.has(config.id)) {
            throw new Error(`智能体ID已存在: ${config.id}`);
        }
    }

    /**
     * 删除自定义智能体
     */
    async deleteCustomAgent(agentId) {
        const agent = this.agents.get(agentId);
        if (!agent) {
            throw new Error(`智能体不存在: ${agentId}`);
        }

        if (agent.type !== 'custom') {
            throw new Error('只能删除自定义智能体');
        }

        // 删除文件
        if (fs.existsSync(agent.configPath)) {
            fs.unlinkSync(agent.configPath);
        }

        // 从内存中移除
        this.agents.delete(agentId);

        // 如果是当前智能体，清除选择
        if (this.currentAgent && this.currentAgent.id === agentId) {
            this.currentAgent = null;
        }

        this.emit('agentDeleted', agentId);
        console.log(`删除智能体: ${agent.name}`);
    }

    /**
     * 获取智能体统计信息
     */
    getStatistics() {
        const stats = {
            total: this.agents.size,
            builtin: 0,
            custom: 0,
            categories: {}
        };

        for (const agent of this.agents.values()) {
            if (agent.type === 'builtin') {
                stats.builtin++;
            } else {
                stats.custom++;
            }

            const category = agent.category || 'other';
            stats.categories[category] = (stats.categories[category] || 0) + 1;
        }

        return stats;
    }
}

module.exports = AgentManager;

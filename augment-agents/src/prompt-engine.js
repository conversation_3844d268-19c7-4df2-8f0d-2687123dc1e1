/**
 * 提示词引擎
 * 负责提示词的模板渲染、变量替换和上下文注入
 */

const fs = require('fs');
const path = require('path');

class PromptEngine {
    constructor(configPath, settings) {
        this.configPath = configPath;
        this.settings = settings;
        this.templates = new Map();
        this.variables = new Map();
    }

    /**
     * 初始化提示词引擎
     */
    async init() {
        await this.loadTemplates();
        this.setupDefaultVariables();
        console.log('提示词引擎初始化完成');
    }

    /**
     * 加载提示词模板
     */
    async loadTemplates() {
        const templatesPath = path.join(this.configPath, 'prompts', 'templates');
        
        if (fs.existsSync(templatesPath)) {
            const files = fs.readdirSync(templatesPath);
            
            for (const file of files) {
                if (path.extname(file) === '.md') {
                    const templateName = path.basename(file, '.md');
                    const templatePath = path.join(templatesPath, file);
                    const templateContent = fs.readFileSync(templatePath, 'utf8');
                    
                    this.templates.set(templateName, templateContent);
                }
            }
        }
    }

    /**
     * 设置默认变量
     */
    setupDefaultVariables() {
        this.variables.set('timestamp', () => new Date().toISOString());
        this.variables.set('date', () => new Date().toLocaleDateString('zh-CN'));
        this.variables.set('time', () => new Date().toLocaleTimeString('zh-CN'));
        this.variables.set('language', this.settings.language);
        this.variables.set('version', this.settings.version);
    }

    /**
     * 生成智能体提示词
     */
    async generatePrompt(agent, promptType, context = {}) {
        let promptContent = '';

        // 获取基础提示词
        if (agent.loadedPrompts && agent.loadedPrompts[promptType]) {
            promptContent = agent.loadedPrompts[promptType];
        } else {
            // 使用默认模板
            const template = this.templates.get(promptType);
            if (template) {
                promptContent = template;
            } else {
                throw new Error(`提示词模板不存在: ${promptType}`);
            }
        }

        // 准备变量上下文
        const variables = this.prepareVariables(agent, context);

        // 渲染模板
        const renderedPrompt = this.renderTemplate(promptContent, variables);

        return renderedPrompt;
    }

    /**
     * 准备变量上下文
     */
    prepareVariables(agent, context) {
        const variables = {
            // 智能体信息
            agentName: agent.name,
            agentId: agent.id,
            agentDescription: agent.description,
            agentVersion: agent.version,
            
            // 系统信息
            language: this.settings.language,
            timestamp: new Date().toISOString(),
            date: new Date().toLocaleDateString('zh-CN'),
            time: new Date().toLocaleTimeString('zh-CN'),
            
            // 用户上下文
            ...context
        };

        // 添加动态变量
        for (const [key, value] of this.variables.entries()) {
            if (typeof value === 'function') {
                variables[key] = value();
            } else {
                variables[key] = value;
            }
        }

        return variables;
    }

    /**
     * 渲染模板
     */
    renderTemplate(template, variables) {
        let rendered = template;

        // 简单的变量替换（类似Mustache）
        const prefix = this.settings.prompts.variablePrefix;
        const suffix = this.settings.prompts.variableSuffix;

        for (const [key, value] of Object.entries(variables)) {
            const placeholder = `${prefix}${key}${suffix}`;
            const regex = new RegExp(this.escapeRegex(placeholder), 'g');
            rendered = rendered.replace(regex, String(value));
        }

        return rendered;
    }

    /**
     * 转义正则表达式特殊字符
     */
    escapeRegex(string) {
        return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    }

    /**
     * 设置变量
     */
    setVariable(key, value) {
        this.variables.set(key, value);
    }

    /**
     * 获取变量
     */
    getVariable(key) {
        return this.variables.get(key);
    }

    /**
     * 注入上下文信息
     */
    async injectContext(promptContent, contextData) {
        if (!this.settings.prompts.enableContextInjection) {
            return promptContent;
        }

        let injectedContent = promptContent;

        // 注入项目信息
        if (contextData.projectInfo) {
            const projectSection = this.formatProjectInfo(contextData.projectInfo);
            injectedContent = `${projectSection}\n\n${injectedContent}`;
        }

        // 注入代码上下文
        if (contextData.codeContext) {
            const codeSection = this.formatCodeContext(contextData.codeContext);
            injectedContent = `${injectedContent}\n\n${codeSection}`;
        }

        return injectedContent;
    }

    /**
     * 格式化项目信息
     */
    formatProjectInfo(projectInfo) {
        return `## 项目上下文

**项目名称**: ${projectInfo.name || '未知'}
**项目类型**: ${projectInfo.type || '未知'}
**主要技术**: ${projectInfo.technologies ? projectInfo.technologies.join(', ') : '未知'}
**项目描述**: ${projectInfo.description || '无描述'}`;
    }

    /**
     * 格式化代码上下文
     */
    formatCodeContext(codeContext) {
        let formatted = '## 代码上下文\n\n';

        if (codeContext.currentFile) {
            formatted += `**当前文件**: ${codeContext.currentFile}\n`;
        }

        if (codeContext.relatedFiles && codeContext.relatedFiles.length > 0) {
            formatted += `**相关文件**: ${codeContext.relatedFiles.join(', ')}\n`;
        }

        if (codeContext.functions && codeContext.functions.length > 0) {
            formatted += `**相关函数**: ${codeContext.functions.join(', ')}\n`;
        }

        return formatted;
    }
}

module.exports = PromptEngine;

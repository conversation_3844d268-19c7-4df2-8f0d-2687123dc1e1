/**
 * Augment Code 智能体系统主入口
 * 提供统一的API接口和初始化逻辑
 */

const AgentManager = require('./agent-manager');
const PromptEngine = require('./prompt-engine');
const { MCPIntegration } = require('./mcp-integration');
const EventEmitter = require('events');

class AugmentAgentSystem extends EventEmitter {
    constructor(options = {}) {
        super();
        
        this.options = {
            configPath: options.configPath || './config',
            enableMCP: options.enableMCP !== false,
            autoInit: options.autoInit !== false,
            ...options
        };

        this.agentManager = null;
        this.mcpIntegration = null;
        this.isInitialized = false;
    }

    /**
     * 初始化智能体系统
     */
    async init() {
        try {
            console.log('正在初始化Augment智能体系统...');

            // 初始化智能体管理器
            this.agentManager = new AgentManager(this.options.configPath);
            await this.agentManager.init();

            // 初始化MCP集成
            if (this.options.enableMCP) {
                this.mcpIntegration = new MCPIntegration(this.agentManager.settings);
                await this.mcpIntegration.init();
            }

            // 设置事件监听
            this.setupEventListeners();

            this.isInitialized = true;
            this.emit('initialized');
            console.log('Augment智能体系统初始化完成');

            return this;
        } catch (error) {
            console.error('智能体系统初始化失败:', error);
            this.emit('error', error);
            throw error;
        }
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 智能体管理器事件
        this.agentManager.on('agentSelected', (agent) => {
            this.emit('agentSelected', agent);
        });

        this.agentManager.on('agentCreated', (agent) => {
            this.emit('agentCreated', agent);
        });

        this.agentManager.on('agentDeleted', (agentId) => {
            this.emit('agentDeleted', agentId);
        });

        // MCP集成事件
        if (this.mcpIntegration) {
            this.mcpIntegration.on('initialized', () => {
                this.emit('mcpReady');
            });

            this.mcpIntegration.on('error', (error) => {
                this.emit('mcpError', error);
            });
        }
    }

    /**
     * 获取所有智能体
     */
    getAgents() {
        this.ensureInitialized();
        return this.agentManager.getAgents();
    }

    /**
     * 搜索智能体
     */
    searchAgents(query) {
        this.ensureInitialized();
        return this.agentManager.searchAgents(query);
    }

    /**
     * 选择智能体
     */
    async selectAgent(agentId) {
        this.ensureInitialized();
        const agent = await this.agentManager.selectAgent(agentId);
        
        // 检查MCP工具兼容性
        if (this.mcpIntegration) {
            const compatibility = this.mcpIntegration.checkToolCompatibility(agent);
            if (compatibility.missing.length > 0) {
                console.warn('智能体缺少以下MCP工具:', compatibility.missing);
            }
            if (compatibility.incompatible.length > 0) {
                console.warn('智能体存在不兼容的MCP工具:', compatibility.incompatible);
            }
        }

        return agent;
    }

    /**
     * 获取当前智能体
     */
    getCurrentAgent() {
        this.ensureInitialized();
        return this.agentManager.getCurrentAgent();
    }

    /**
     * 生成智能体提示词
     */
    async generatePrompt(promptType, context = {}) {
        this.ensureInitialized();
        
        const currentAgent = this.getCurrentAgent();
        if (!currentAgent) {
            throw new Error('未选择智能体');
        }

        return await this.agentManager.generatePrompt(promptType, context);
    }

    /**
     * 执行智能体任务
     */
    async executeAgentTask(taskType, params = {}) {
        this.ensureInitialized();
        
        const currentAgent = this.getCurrentAgent();
        if (!currentAgent) {
            throw new Error('未选择智能体');
        }

        // 生成任务提示词
        const prompt = await this.generatePrompt(taskType, params);

        // 如果智能体配置了MCP工具，准备工具上下文
        let toolContext = {};
        if (this.mcpIntegration && currentAgent.mcpTools) {
            toolContext = {
                availableTools: this.mcpIntegration.getAvailableToolsForAgent(currentAgent),
                mcpIntegration: this.mcpIntegration
            };
        }

        return {
            prompt,
            agent: currentAgent,
            toolContext,
            taskType,
            params
        };
    }

    /**
     * 调用MCP工具
     */
    async callMCPTool(toolId, method, params = {}) {
        this.ensureInitialized();
        
        if (!this.mcpIntegration) {
            throw new Error('MCP集成未启用');
        }

        return await this.mcpIntegration.executeTool(toolId, method, params);
    }

    /**
     * 创建自定义智能体
     */
    async createCustomAgent(agentConfig) {
        this.ensureInitialized();
        return await this.agentManager.createCustomAgent(agentConfig);
    }

    /**
     * 删除自定义智能体
     */
    async deleteCustomAgent(agentId) {
        this.ensureInitialized();
        return await this.agentManager.deleteCustomAgent(agentId);
    }

    /**
     * 获取系统统计信息
     */
    getSystemStats() {
        this.ensureInitialized();
        
        const agentStats = this.agentManager.getStatistics();
        const mcpStats = this.mcpIntegration ? this.mcpIntegration.getToolUsageStats() : null;

        return {
            agents: agentStats,
            mcp: mcpStats,
            system: {
                initialized: this.isInitialized,
                uptime: process.uptime(),
                memory: process.memoryUsage()
            }
        };
    }

    /**
     * 获取智能体配置
     */
    getAgentConfig(agentId) {
        this.ensureInitialized();
        return this.agentManager.getAgent(agentId);
    }

    /**
     * 更新智能体配置
     */
    async updateAgentConfig(agentId, updates) {
        this.ensureInitialized();
        
        const agent = this.agentManager.getAgent(agentId);
        if (!agent) {
            throw new Error(`智能体不存在: ${agentId}`);
        }

        if (agent.type !== 'custom') {
            throw new Error('只能修改自定义智能体');
        }

        // 合并更新
        Object.assign(agent, updates);

        // 保存到文件
        const fs = require('fs');
        fs.writeFileSync(agent.configPath, JSON.stringify(agent, null, 2));

        this.emit('agentUpdated', agent);
        return agent;
    }

    /**
     * 导出智能体配置
     */
    exportAgentConfig(agentId) {
        this.ensureInitialized();
        
        const agent = this.agentManager.getAgent(agentId);
        if (!agent) {
            throw new Error(`智能体不存在: ${agentId}`);
        }

        // 移除运行时属性
        const exportConfig = { ...agent };
        delete exportConfig.configPath;
        delete exportConfig.loadedPrompts;

        return exportConfig;
    }

    /**
     * 导入智能体配置
     */
    async importAgentConfig(agentConfig) {
        this.ensureInitialized();
        return await this.createCustomAgent(agentConfig);
    }

    /**
     * 确保系统已初始化
     */
    ensureInitialized() {
        if (!this.isInitialized) {
            throw new Error('智能体系统未初始化，请先调用 init() 方法');
        }
    }

    /**
     * 销毁系统资源
     */
    async destroy() {
        if (this.mcpIntegration && this.mcpIntegration.funiA0McpClient) {
            this.mcpIntegration.funiA0McpClient.disconnect();
        }

        this.removeAllListeners();
        this.isInitialized = false;
        
        console.log('智能体系统已销毁');
    }
}

// 创建全局实例
let globalInstance = null;

/**
 * 获取全局智能体系统实例
 */
function getGlobalInstance(options = {}) {
    if (!globalInstance) {
        globalInstance = new AugmentAgentSystem(options);
        
        if (options.autoInit !== false) {
            globalInstance.init().catch(console.error);
        }
    }
    return globalInstance;
}

/**
 * 重置全局实例
 */
function resetGlobalInstance() {
    if (globalInstance) {
        globalInstance.destroy();
        globalInstance = null;
    }
}

module.exports = {
    AugmentAgentSystem,
    getGlobalInstance,
    resetGlobalInstance,
    AgentManager,
    PromptEngine,
    MCPIntegration
};

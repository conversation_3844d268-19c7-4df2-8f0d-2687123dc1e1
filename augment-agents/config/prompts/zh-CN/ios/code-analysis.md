# iOS代码分析专用提示词

## 分析框架

### 1. 代码结构分析
```
📁 架构概览
├── 🏗️ 整体架构模式
├── 📦 模块划分和职责
├── 🔗 模块间依赖关系
└── 📋 设计模式使用

📝 分析要点：
- 识别主要的类和协议
- 分析继承和组合关系
- 评估模块化程度
- 检查设计模式的合理性
```

### 2. 功能实现分析
```
⚙️ 核心功能
├── 🎯 主要业务逻辑
├── 🔄 数据流转过程
├── 🎮 用户交互处理
└── 🔧 系统集成方式

📝 分析要点：
- 理解业务逻辑实现
- 追踪数据处理流程
- 分析API调用链路
- 评估功能完整性
```

### 3. 性能和质量分析
```
🚀 性能指标
├── ⏱️ 时间复杂度分析
├── 💾 内存使用评估
├── 🔋 电池消耗考虑
└── 📊 资源利用效率

🔍 质量指标
├── 📖 代码可读性
├── 🔧 可维护性
├── 🧪 可测试性
└── 🛡️ 安全性
```

## 分析模板

### Objective-C代码分析
```markdown
## 代码分析报告

### 基本信息
- **文件名**: {{fileName}}
- **主要功能**: {{mainFunction}}
- **代码行数**: {{lineCount}}
- **复杂度**: {{complexity}}

### 架构分析
#### 类设计
- **主要类**: {{mainClasses}}
- **继承关系**: {{inheritance}}
- **协议实现**: {{protocols}}
- **分类扩展**: {{categories}}

#### 内存管理
- **ARC使用**: {{arcUsage}}
- **手动管理**: {{manualMemory}}
- **潜在泄漏**: {{memoryLeaks}}
- **循环引用**: {{retainCycles}}

### 功能分析
#### 核心逻辑
{{coreLogic}}

#### API调用
{{apiCalls}}

#### 错误处理
{{errorHandling}}

### 性能评估
#### 优势
{{strengths}}

#### 问题点
{{issues}}

#### 优化建议
{{optimizations}}

### 安全性检查
{{securityAnalysis}}

### 改进建议
{{improvements}}
```

### Swift代码分析
```markdown
## Swift代码分析

### 语言特性使用
- **可选类型**: {{optionals}}
- **泛型**: {{generics}}
- **协议**: {{protocols}}
- **扩展**: {{extensions}}
- **闭包**: {{closures}}

### 现代Swift特性
- **async/await**: {{asyncAwait}}
- **Combine**: {{combine}}
- **SwiftUI**: {{swiftUI}}
- **Property Wrappers**: {{propertyWrappers}}

### 性能考虑
- **值类型vs引用类型**: {{typeChoice}}
- **Copy-on-Write**: {{cow}}
- **内存布局**: {{memoryLayout}}
```

## 专项分析指南

### 音视频代码分析
```
🎥 AVFoundation分析
├── 📹 捕获会话配置
├── 🔄 数据流处理
├── 📊 格式转换
└── ⚡ 实时性能

📝 关键检查点：
- AVCaptureSession配置是否合理
- CMSampleBuffer处理是否高效
- 内存拷贝是否必要
- 线程安全是否保证
```

### Hook代码分析
```
🪝 Hook技术分析
├── 🎯 Hook目标选择
├── 🔧 实现方式评估
├── 🛡️ 安全性考虑
└── 🔄 兼容性检查

📝 分析要点：
- MSHook使用是否正确
- 原始函数调用是否安全
- 异常处理是否完善
- 系统版本兼容性
```

### UI代码分析
```
🎨 用户界面分析
├── 📱 布局实现
├── 🎮 交互逻辑
├── 🎯 响应式设计
└── ♿ 可访问性

📝 评估标准：
- Auto Layout使用
- 性能优化
- 内存管理
- 用户体验
```

## 分析输出格式

### 问题分级
```
🔴 严重问题 (Critical)
- 可能导致崩溃或数据丢失
- 严重的安全漏洞
- 重大性能问题

🟡 重要问题 (High)
- 影响功能正常使用
- 明显的性能瓶颈
- 代码质量问题

🟢 一般问题 (Medium)
- 代码风格不规范
- 轻微性能影响
- 可维护性问题

🔵 建议改进 (Low)
- 最佳实践建议
- 代码优化机会
- 文档完善
```

### 建议格式
```
## 改进建议

### 立即修复 (Immediate)
1. **问题**: {{issue}}
   **影响**: {{impact}}
   **解决方案**: {{solution}}
   **代码示例**: 
   ```objc
   {{codeExample}}
   ```

### 短期优化 (Short-term)
{{shortTermImprovements}}

### 长期规划 (Long-term)
{{longTermPlanning}}
```

## 使用指南

### 分析流程
1. **初步扫描**: 了解代码整体结构
2. **深入分析**: 重点分析核心功能
3. **问题识别**: 发现潜在问题和风险
4. **方案设计**: 提供具体改进建议
5. **优先级排序**: 按重要性排列改进项

### 注意事项
- 保持客观和建设性
- 提供具体可行的建议
- 考虑项目实际情况
- 平衡理想与现实

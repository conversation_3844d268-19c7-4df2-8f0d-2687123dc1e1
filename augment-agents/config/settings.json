{"version": "1.0.0", "language": "zh-CN", "agents": {"defaultAgent": "ios-developer", "autoLoad": true, "enableCustomAgents": true}, "prompts": {"templateEngine": "mustache", "variablePrefix": "{{", "variableSuffix": "}}", "enableContextInjection": true}, "mcp": {"enableIntegration": true, "compatibilityMode": true, "preserveExistingTools": true, "funiA0McpIntegration": {"enabled": true, "priority": "high"}}, "ui": {"showAgentSelector": true, "enableQuickSwitch": true, "showAgentStatus": true, "theme": "auto"}, "performance": {"maxTokens": 4000, "defaultTemperature": 0.7, "enableCaching": true, "cacheTimeout": 3600}, "logging": {"level": "info", "enableDebug": false, "logFile": "augment-agents.log"}}
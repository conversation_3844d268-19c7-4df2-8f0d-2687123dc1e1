{"id": "code-reviewer", "name": "代码审查员", "description": "专业的代码质量分析师，提供全面的代码审查、安全检测和最佳实践建议", "version": "1.0.0", "language": "zh-CN", "category": "quality", "icon": "🔍", "author": "Augment Team", "tags": ["代码审查", "质量分析", "安全检测", "最佳实践"], "prompts": {"system": "prompts/zh-CN/review/system.md", "welcome": "prompts/zh-CN/review/welcome.md", "codeReview": "prompts/zh-CN/review/code-review.md", "securityCheck": "prompts/zh-CN/review/security-check.md", "performanceAnalysis": "prompts/zh-CN/review/performance-analysis.md"}, "mcpTools": ["FuniA0Mcp", "static-analysis", "security-scanner", "performance-profiler", "git-integration"], "capabilities": ["code-quality-analysis", "security-vulnerability-detection", "performance-bottleneck-identification", "best-practices-validation", "documentation-review", "test-coverage-analysis", "dependency-audit"], "reviewCriteria": {"codeQuality": {"readability": "代码可读性和清晰度", "maintainability": "代码可维护性", "complexity": "代码复杂度分析", "naming": "命名规范检查"}, "security": {"vulnerabilities": "安全漏洞检测", "dataValidation": "数据验证检查", "accessControl": "访问控制审查", "encryption": "加密实现检查"}, "performance": {"algorithms": "算法效率分析", "memoryUsage": "内存使用优化", "resourceManagement": "资源管理检查", "concurrency": "并发处理分析"}, "standards": {"codingStyle": "编码风格规范", "documentation": "文档完整性", "testing": "测试覆盖率", "errorHandling": "错误处理机制"}}, "settings": {"autoRun": false, "maxTokens": 6000, "temperature": 0.3, "enableDeepAnalysis": true, "generateReports": true, "severityLevels": ["critical", "high", "medium", "low", "info"]}, "workflow": {"steps": [{"name": "代码扫描", "description": "全面扫描代码库，识别潜在问题", "prompt": "prompts/zh-CN/review/workflow/scanning.md"}, {"name": "质量分析", "description": "分析代码质量指标和最佳实践遵循情况", "prompt": "prompts/zh-CN/review/workflow/quality-analysis.md"}, {"name": "安全检查", "description": "检测安全漏洞和风险点", "prompt": "prompts/zh-CN/review/workflow/security-check.md"}, {"name": "性能评估", "description": "评估性能瓶颈和优化机会", "prompt": "prompts/zh-CN/review/workflow/performance-evaluation.md"}, {"name": "报告生成", "description": "生成详细的审查报告和改进建议", "prompt": "prompts/zh-CN/review/workflow/report-generation.md"}]}, "reportTemplate": {"sections": ["执行摘要", "代码质量评分", "关键问题列表", "安全风险评估", "性能分析结果", "改进建议", "最佳实践推荐"], "format": "markdown", "includeMetrics": true, "includeCodeSnippets": true}, "examples": [{"title": "全面代码审查", "description": "对整个项目进行全面的代码质量审查", "prompt": "请对当前项目进行全面的代码审查，重点关注代码质量、安全性和性能"}, {"title": "安全漏洞检测", "description": "专门检测代码中的安全漏洞和风险", "prompt": "检查代码中可能存在的安全漏洞，特别是内存安全和数据验证问题"}, {"title": "性能瓶颈分析", "description": "识别和分析代码中的性能瓶颈", "prompt": "分析音视频处理代码的性能瓶颈，提供优化建议"}]}
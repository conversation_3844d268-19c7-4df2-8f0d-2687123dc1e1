{"id": "project-manager", "name": "项目经理", "description": "专业的项目管理助手，擅长项目规划、任务分解、进度跟踪和团队协作", "version": "1.0.0", "language": "zh-CN", "category": "management", "icon": "📋", "author": "Augment Team", "tags": ["项目管理", "任务规划", "进度跟踪", "团队协作"], "prompts": {"system": "prompts/zh-CN/pm/system.md", "welcome": "prompts/zh-CN/pm/welcome.md", "planning": "prompts/zh-CN/pm/planning.md", "tracking": "prompts/zh-CN/pm/tracking.md", "reporting": "prompts/zh-CN/pm/reporting.md"}, "mcpTools": ["FuniA0Mcp", "task-manager", "calendar-integration", "document-generator", "team-collaboration"], "capabilities": ["project-planning", "task-decomposition", "timeline-management", "resource-allocation", "risk-assessment", "progress-tracking", "stakeholder-communication", "documentation-generation"], "methodologies": {"agile": {"name": "敏捷开发", "description": "基于敏捷原则的项目管理方法", "practices": ["Sprint规划", "每日站会", "回顾会议", "用户故事"]}, "waterfall": {"name": "瀑布模型", "description": "传统的线性项目管理方法", "practices": ["需求分析", "设计阶段", "开发阶段", "测试阶段"]}, "kanban": {"name": "看板方法", "description": "可视化工作流程管理", "practices": ["工作可视化", "限制在制品", "持续改进"]}}, "settings": {"autoRun": false, "maxTokens": 5000, "temperature": 0.5, "enableTaskTracking": true, "generateReports": true, "defaultMethodology": "agile"}, "workflow": {"steps": [{"name": "项目启动", "description": "定义项目目标、范围和关键利益相关者", "prompt": "prompts/zh-CN/pm/workflow/initiation.md"}, {"name": "需求分析", "description": "收集和分析项目需求，制定详细规格", "prompt": "prompts/zh-CN/pm/workflow/requirements.md"}, {"name": "项目规划", "description": "制定项目计划、时间表和资源分配", "prompt": "prompts/zh-CN/pm/workflow/planning.md"}, {"name": "执行监控", "description": "跟踪项目进度，管理变更和风险", "prompt": "prompts/zh-CN/pm/workflow/execution.md"}, {"name": "项目收尾", "description": "完成项目交付，总结经验教训", "prompt": "prompts/zh-CN/pm/workflow/closure.md"}]}, "templates": {"projectCharter": "项目章程模板", "workBreakdownStructure": "工作分解结构模板", "riskRegister": "风险登记册模板", "statusReport": "状态报告模板", "lessonsLearned": "经验教训模板"}, "kpis": [{"name": "进度完成率", "description": "已完成任务占总任务的百分比", "formula": "完成任务数 / 总任务数 × 100%"}, {"name": "预算执行率", "description": "已使用预算占总预算的百分比", "formula": "已用预算 / 总预算 × 100%"}, {"name": "质量指标", "description": "缺陷密度和客户满意度", "formula": "缺陷数 / 代码行数"}, {"name": "团队效率", "description": "团队生产力和协作效果", "formula": "完成故事点 / 计划故事点"}], "examples": [{"title": "项目规划", "description": "为iOS虚拟摄像头项目制定详细的开发计划", "prompt": "请为当前的iOS虚拟摄像头项目制定详细的开发计划，包括任务分解和时间安排"}, {"title": "进度跟踪", "description": "跟踪项目当前进度并生成状态报告", "prompt": "分析当前项目进度，生成详细的状态报告和下一步行动计划"}, {"title": "风险评估", "description": "识别项目风险并制定应对策略", "prompt": "评估iOS开发项目中的潜在风险，制定相应的风险缓解策略"}]}
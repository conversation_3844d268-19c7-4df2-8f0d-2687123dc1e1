{"name": "augment-agents", "version": "1.0.0", "description": "Augment Code 智能体系统 - 基于 Trae IDE 智能体设计理念的智能助手配置和管理系统", "main": "src/index.js", "scripts": {"start": "node src/index.js", "test": "node test/validation-test.js", "example": "node examples/usage-examples.js", "validate": "npm run test", "dev": "node --inspect src/index.js", "lint": "eslint src/ examples/ test/", "format": "prettier --write src/ examples/ test/ config/"}, "keywords": ["augment-code", "ai-agent", "intelligent-assistant", "mcp-integration", "ios-development", "code-analysis", "project-management", "chinese-prompts"], "author": "Augment Team", "license": "MIT", "engines": {"node": ">=14.0.0"}, "dependencies": {"events": "^3.3.0"}, "devDependencies": {"eslint": "^8.0.0", "prettier": "^2.8.0", "nodemon": "^2.0.20"}, "repository": {"type": "git", "url": "https://github.com/augment-code/augment-agents.git"}, "bugs": {"url": "https://github.com/augment-code/augment-agents/issues"}, "homepage": "https://github.com/augment-code/augment-agents#readme", "files": ["src/", "config/", "examples/", "docs/", "README.md"], "directories": {"lib": "src", "test": "test", "doc": "docs", "example": "examples"}, "config": {"defaultConfigPath": "./config", "supportedLanguages": ["zh-CN", "en-US"], "mcpIntegration": true}}
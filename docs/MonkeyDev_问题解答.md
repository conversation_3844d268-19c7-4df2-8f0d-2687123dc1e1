# MonkeyDev 详细问题解答

## 问题1: 配置文件详细注释

### Makefile配置详解
```makefile
# 目标平台配置：指定编译目标
TARGET := iphone:clang:latest:9
# 解释：
# - iphone: 目标平台为iPhone/iOS
# - clang: 使用clang编译器（Apple官方编译器）
# - latest: 使用最新的iOS SDK版本
# - 9: 最低支持iOS 9.0系统

# 安装后重启的进程列表
INSTALL_TARGET_PROCESSES = SpringBoard
# 解释：
# - SpringBoard是iOS的桌面管理进程
# - 插件注入UIKit后需要重启SpringBoard才能生效
# - 可以添加多个进程，用空格分隔

# 测试设备IP地址（开发时使用）
THEOS_DEVICE_IP=***********
# 解释：
# - 用于make install命令自动部署到设备
# - 必须是同一局域网内的越狱iOS设备IP
# - 需要根据实际设备IP修改

# Tweak项目名称
TWEAK_NAME = TTtest
# 解释：
# - 生成的动态库文件名：TTtest.dylib
# - 对应的plist文件名：TTtest.plist
# - 最终deb包名的一部分

# 源文件列表
TTtest_FILES = Tweak.x
# 解释：
# - 指定需要编译的源代码文件
# - 支持.x（Logos）、.m/.mm（Objective-C）、.c/.cpp（C/C++）
# - 多个文件用空格分隔

# 编译器标志
TTtest_CFLAGS = -fobjc-arc
# 解释：
# - -fobjc-arc: 启用自动引用计数，简化内存管理
# - 其他常用标志：-framework UIKit, -I./headers
```

### control文件详解
```bash
# Debian包的唯一标识符
Package: com.if-she.cydia.vcam
# 解释：
# - 反向域名格式，确保全局唯一
# - 用于包管理系统识别和更新
# - 建议格式：com.作者.项目名

# 在Cydia中显示的名称
Name: VCAM4iOS
# 解释：
# - 用户在Cydia中看到的插件名称
# - 支持中文和特殊字符
# - 建议简洁明了

# 版本号
Version: 0.0.1
# 解释：
# - 用于版本管理和更新检测
# - 格式：主版本.次版本.修订版本
# - 新版本号必须大于旧版本

# 目标设备架构
Architecture: iphoneos-arm
# 解释：
# - iphoneos-arm: 支持所有ARM架构的iOS设备
# - arm64: 仅支持64位设备（iPhone 5s及以后）
# - armv7: 仅支持32位设备（已基本淘汰）

# 依赖关系
Depends: mobilesubstrate (>= 0.9.5000)
# 解释：
# - 指定插件运行所需的其他包
# - mobilesubstrate: 越狱插件的核心框架
# - 版本号要求：>= 表示大于等于指定版本
# - 多个依赖用逗号分隔

# 其他可选字段：
# Description: 插件功能的详细描述
# Maintainer: 维护者邮箱
# Author: 作者信息
# Section: 分类（Tweaks、Utilities、System等）
# Homepage: 项目主页
# Depiction: 详细说明页面URL
```

### 过滤器配置详解
```plist
{
    Filter = {
        # Bundle过滤器：指定注入的Framework
        Bundles = ( "com.apple.UIKit" );
        # 解释：
        # - UIKit: iOS用户界面框架，几乎所有App都使用
        # - 意味着插件会注入到所有使用UIKit的应用
        # - 其他常用Bundle：
        #   "com.apple.Foundation" - 基础框架
        #   "com.apple.AVFoundation" - 音视频框架
        #   "com.apple.CoreGraphics" - 图形框架
        
        # 可执行文件过滤器（可选）
        # Executables = ("SpringBoard", "Camera");
        # 解释：
        # - 仅注入指定的可执行文件
        # - 比Bundle过滤器更精确
        # - 可以减少不必要的注入，提高性能
        
        # 类过滤器（可选）
        # Classes = ("UIViewController", "UIView");
        # 解释：
        # - 仅在目标类存在时才注入
        # - 用于避免在不相关的应用中加载
        
        # 注入模式（可选）
        # Mode = "Any";  # Any: 满足任一条件即注入
                        # All: 必须满足所有条件才注入
    };
}
```

## 问题2: mobilesubstrate框架依赖关系

### 什么是mobilesubstrate？
- **定义**: iOS越狱环境下的动态库注入框架
- **作用**: 提供Runtime Hook能力，允许修改系统和应用行为
- **核心功能**:
  - Method Swizzling（方法交换）
  - Function Hooking（函数钩子）
  - 动态库注入管理
  - 安全沙箱绕过

### 环境满足情况分析

| 环境类型 | mobilesubstrate支持 | 详细说明 |
|----------|-------------------|----------|
| **Xcode开发环境** | ❌ 不包含 | 需要MonkeyDev提供模拟支持 |
| **iOS模拟器** | ❌ 不支持 | 模拟器架构不同，无法运行越狱插件 |
| **越狱iOS设备** | ✅ 自动包含 | 越狱过程会自动安装mobilesubstrate |
| **非越狱设备** | ❌ 无法安装 | 苹果代码签名机制阻止 |

### 开发环境配置
```bash
# 越狱设备端（自动满足）
- mobilesubstrate (>= 0.9.5000)    # 核心注入框架
- Cydia Substrate                  # 包管理器
- PreferenceLoader                 # 设置界面支持（可选）

# 开发机端（通过MonkeyDev满足）
- MonkeyDev                        # 提供mobilesubstrate开发环境
- Xcode + iOS SDK                  # 开发工具链
- libsubstrate.dylib               # MonkeyDev提供的模拟库
```

## 问题3: Theos环境配置

### Theos的用途
- **定义**: 越狱开发的跨平台构建系统
- **核心功能**:
  - 将Logos语法编译为Objective-C代码
  - 提供Makefile模板和构建工具
  - 生成.deb安装包
  - 自动部署到测试设备
  - 管理项目依赖关系

### 当前项目是否需要Theos？

| 开发方式 | Theos需求 | 说明 |
|----------|-----------|------|
| **传统命令行开发** | ✅ 必需 | 使用`make`命令构建，需要完整Theos环境 |
| **MonkeyDev开发** | ❌ 不需要 | MonkeyDev内置Theos功能，通过Xcode构建 |
| **混合开发** | ⚠️ 可选 | 可保留Theos用于命令行快速构建 |

### 推荐配置
```bash
# 推荐方案：仅使用MonkeyDev
# 优势：环境简化，统一IDE开发体验
# 无需单独安装Theos

# 可选方案：同时保留Theos
# 适用场景：需要命令行批量构建、CI/CD集成
sudo git clone --recursive https://github.com/theos/theos.git /opt/theos
export THEOS=/opt/theos
```

## 问题4: MonkeyDev Device IP配置

### IP地址说明
- ************* 不是固定值**，需要修改为实际设备IP
- **必须要求**: 开发机和iOS设备在同一局域网内
- **动态性**: WiFi重连后IP可能会变化，需要及时更新

### 如何获取设备IP
```bash
# 方法1: iOS设备设置
设置 → WiFi → 点击已连接网络的(i)图标 → 查看IP地址

# 方法2: SSH连接后查看
ssh root@设备IP
ifconfig en0 | grep "inet " | awk '{print $2}'

# 方法3: 路由器管理界面
登录路由器 → 查看已连接设备列表

# 方法4: 网络扫描工具
nmap -sn ***********/24  # 扫描整个网段
```

### 网络配置要求
```bash
# 网络拓扑示例
开发机(macOS) ←→ 路由器 ←→ iOS设备(越狱)
*************      WiFi      ***********

# 连接测试
ping ***********                    # 测试网络连通性
ssh root@***********                # 测试SSH连接
ssh root@*********** "uname -a"     # 测试设备信息
```

## 问题5: MonkeyDev Settings详解

### 完整配置项说明
```bash
# === 设备连接配置 ===
MonkeyDev Device IP: ***********
# 用途: 目标设备的IP地址
# 配置: Xcode项目设置 → Build Settings → MonkeyDev

MonkeyDev Device Port: 22
# 用途: SSH连接端口
# 默认: 22（标准SSH端口）

MonkeyDev Device User: root
# 用途: SSH登录用户名
# 默认: root（越狱设备默认用户）

MonkeyDev Device Password: alpine
# 用途: SSH登录密码
# 默认: alpine（建议修改为更安全的密码）

# === 构建行为配置 ===
Install on Build: YES
# 用途: 构建完成后自动安装到设备
# 选项: YES/NO

MonkeyDev Kill SpringBoard: YES
# 用途: 安装后自动重启SpringBoard使插件生效
# 选项: YES/NO

MonkeyDev Clean Build Folder: NO
# 用途: 构建前是否清理输出文件夹
# 选项: YES/NO

# === 安装路径配置 ===
MonkeyDev Install Path: /Library/MobileSubstrate/DynamicLibraries/
# 用途: 插件在设备上的安装路径
# 文件: TTtest.dylib + TTtest.plist

# === 调试配置 ===
MonkeyDev Enable Debug: YES
# 用途: 启用调试模式，支持断点调试
# 选项: YES/NO

MonkeyDev Debug Port: 1234
# 用途: 调试服务端口
# 默认: 1234

MonkeyDev Enable Logging: YES
# 用途: 启用详细日志输出
# 选项: YES/NO
```

### 配置方法
```bash
# 在Xcode中配置
1. 选择项目 → Build Settings
2. 搜索 "MonkeyDev"
3. 修改相应配置项
4. 或在项目的User-Defined设置中添加
```

## 问题6: 插件部署方式

### 开发阶段部署（SSH方式）
```bash
# 1. MonkeyDev自动部署
# 触发方式: Xcode中按⌘+R或Build
# 流程: 编译 → SSH传输 → 安装 → 重启SpringBoard
# 优势: 快速迭代，实时调试
# 要求: 设备和开发机同网络，SSH连接正常

# 2. 手动SSH部署
scp TTtest.dylib root@***********:/Library/MobileSubstrate/DynamicLibraries/
scp TTtest.plist root@***********:/Library/MobileSubstrate/DynamicLibraries/
ssh root@*********** "killall SpringBoard"

# 3. deb包安装
make package  # 生成deb包
scp *.deb root@***********:/tmp/
ssh root@*********** "dpkg -i /tmp/*.deb"
```

### 发布阶段部署（Cydia方式）
```bash
# 1. 用户通过Cydia安装
# 流程: 
# a. 用户打开Cydia
# b. 搜索插件名称或浏览分类
# c. 点击安装，自动下载deb包
# d. 自动安装并处理依赖关系
# e. 提示重启SpringBoard

# 2. 发布到Cydia源
# a. 准备发布包
make package FINALPACKAGE=1  # 生成发布版本

# b. 上传到软件源
# 个人源: 需要搭建APT服务器
# 公共源: BigBoss、ModMyi等（需要审核）

# c. 用户添加源
# 设置 → Cydia → 软件源 → 编辑 → 添加 → 输入源地址
```

### 部署方式对比

| 部署方式 | 适用阶段 | 优势 | 劣势 |
|----------|----------|------|------|
| **SSH自动部署** | 开发调试 | 快速迭代，实时调试 | 需要网络连接，仅限开发者 |
| **SSH手动部署** | 测试验证 | 灵活控制，可批量操作 | 操作繁琐，容易出错 |
| **deb包安装** | 内测分发 | 标准化安装，易于分发 | 需要手动传输和安装 |
| **Cydia安装** | 正式发布 | 用户友好，自动更新 | 需要审核，发布周期长 |

---

**总结**: 开发阶段使用SSH部署进行快速迭代，发布阶段通过Cydia为用户提供便捷的安装体验。

# iOS-VCAM 项目文档

## 项目概述

iOS-VCAM 是一个基于 Cydia Substrate 的虚拟摄像头项目，能够在越狱的 iOS 设备上替换系统摄像头画面。本项目提供了两套完整的商业化方案设计。

## 文档结构

```
docs/
├── README.md                           # 项目文档总览（本文件）
├── 传统云端会员系统方案.md              # 传统云端架构的会员系统设计
├── 去中心化付费验证方案.md              # 基于区块链的去中心化验证方案
└── 项目架构分析报告.md                  # 原项目的详细技术分析（待创建）
```

## 方案对比

### 传统云端会员系统方案

**适用场景：**
- 需要稳定可靠的商业化产品
- 有充足的服务器运维资源
- 用户群体对隐私要求不高
- 需要详细的用户数据分析

**核心特点：**
- ✅ 技术成熟，实施风险低
- ✅ 用户体验友好
- ✅ 支持多种支付方式
- ✅ 完善的数据分析和监控
- ❌ 需要中心化服务器
- ❌ 存在单点故障风险
- ❌ 作者身份可能暴露

**技术栈：**
- 后端：Python Flask + MySQL + Redis
- 客户端：Objective-C + Theos
- 部署：Docker + Nginx + 云服务器
- 监控：Prometheus + Grafana

### 去中心化付费验证方案

**适用场景：**
- 注重隐私保护和匿名性
- 希望避免法律风险
- 用户群体技术水平较高
- 不希望依赖中心化服务

**核心特点：**
- ✅ 完全匿名，保护作者隐私
- ✅ 去中心化，无单点故障
- ✅ 抗审查能力强
- ✅ 使用加密货币支付
- ❌ 技术复杂度高
- ❌ 用户使用门槛高
- ❌ 网络依赖性强

**技术栈：**
- 区块链：Ethereum/BSC 智能合约
- 存储：IPFS 分布式网络
- 验证：P2P 共识网络
- 支付：USDT 加密货币
- 隐私：Tor 网络 + 代码混淆

## 快速开始

### 1. 环境准备

**基础要求：**
- 已越狱的 iOS 设备（iOS 11.0+）
- Theos 开发环境
- Xcode 命令行工具

**安装 Theos：**
```bash
# 安装 Theos
bash -c "$(curl -fsSL https://raw.githubusercontent.com/theos/theos/master/bin/install-theos)"

# 设置环境变量
export THEOS=/opt/theos
export PATH="$THEOS/bin:$PATH"
```

### 2. 编译项目

```bash
# 克隆项目
git clone <repository-url>
cd IOS-VCAM-develop

# 配置设备 IP（修改 Makefile）
vim Makefile
# 修改 THEOS_DEVICE_IP=你的设备IP

# 编译
make clean
make

# 打包
make package

# 安装到设备
make install
```

### 3. 选择实施方案

#### 方案一：传统云端系统

1. **阅读文档：** `docs/传统云端会员系统方案.md`
2. **部署服务端：** 按照文档中的 Docker Compose 配置部署
3. **集成客户端：** 将会员验证代码集成到 Tweak.x 中
4. **配置支付：** 集成支付宝、微信支付或 Apple Pay

#### 方案二：去中心化系统

1. **阅读文档：** `docs/去中心化付费验证方案.md`
2. **部署智能合约：** 在以太坊或 BSC 上部署订阅合约
3. **搭建 IPFS 节点：** 建立分布式存储网络
4. **集成验证逻辑：** 将去中心化验证代码集成到项目中

## 使用说明

### 基本操作

1. **安装插件后重启 SpringBoard**
2. **打开任意支持摄像头的 App**
3. **触发功能菜单：**
   - 完整模式：音量+ → 音量-（1秒内）
   - 便捷模式：音量- → 音量+（1秒内）
4. **选择功能：**
   - 选择视频：从相册选择替换视频
   - 下载视频：从远程 URL 下载视频
   - 禁用替换：停止视频替换功能

### 会员功能

- **试用版：** 新用户可免费试用 1 次
- **月度会员：** ¥12/月，无限制使用
- **季度会员：** ¥30/季度，优惠价格
- **年度会员：** ¥100/年，最优惠价格

## 开发指南

### 代码结构

```
IOS-VCAM-develop/
├── Tweak.x                    # 主要实现文件
├── Makefile                   # 构建配置
├── control                    # Cydia 包信息
├── util.h                     # 工具函数
├── bak-snip/                  # 代码片段备份
├── release/                   # 编译产物
└── docs/                      # 项目文档
```

### 核心模块

1. **视频帧处理：** `GetFrame` 类负责视频帧的读取和替换
2. **Hook 机制：** 拦截 AVFoundation 相关方法
3. **用户界面：** 音量键触发的功能菜单
4. **会员验证：** 订阅状态验证和设备绑定

### 扩展开发

**添加新功能：**
1. 在 `Tweak.x` 中添加新的 Hook 方法
2. 实现相应的处理逻辑
3. 更新用户界面和菜单选项
4. 测试功能兼容性

**优化性能：**
1. 减少不必要的 Hook 调用
2. 优化视频帧处理算法
3. 使用缓存机制减少重复计算
4. 异步处理耗时操作

## 安全考虑

### 代码保护

1. **代码混淆：** 使用混淆工具保护核心逻辑
2. **反调试：** 检测和防御调试器
3. **完整性验证：** 检查代码是否被篡改
4. **运行时保护：** 防止动态分析工具

### 隐私保护

1. **数据最小化：** 只收集必要的设备信息
2. **本地存储：** 敏感数据使用 Keychain 存储
3. **传输加密：** 网络通信使用 HTTPS/TLS
4. **匿名化处理：** 对用户数据进行脱敏处理

## 法律声明

### 免责声明

本项目仅供技术研究和学习使用。使用本项目可能涉及以下风险：

1. **系统安全：** 修改系统行为可能影响设备稳定性
2. **法律风险：** 在某些地区可能违反相关法律法规
3. **隐私风险：** 不当使用可能侵犯他人隐私权
4. **商业风险：** 商业化使用需要考虑知识产权问题

### 使用条款

1. **仅限学习：** 本项目仅供技术学习和研究使用
2. **自担风险：** 使用者需自行承担所有风险和责任
3. **合规使用：** 使用时需遵守当地法律法规
4. **禁止滥用：** 禁止用于非法或有害目的

## 贡献指南

### 如何贡献

1. **Fork 项目**
2. **创建功能分支**
3. **提交代码更改**
4. **创建 Pull Request**
5. **等待代码审查**

### 代码规范

1. **命名规范：** 使用有意义的变量和函数名
2. **注释要求：** 为复杂逻辑添加详细注释
3. **错误处理：** 妥善处理各种异常情况
4. **测试覆盖：** 为新功能编写测试用例

## 技术支持

### 常见问题

1. **Q: 安装后无法正常工作？**
   A: 检查设备是否已越狱，确保 mobilesubstrate 正常运行

2. **Q: 视频替换后画面异常？**
   A: 确保替换视频的分辨率与原摄像头分辨率匹配

3. **Q: 在某些 App 中无效？**
   A: 部分 App 可能使用了特殊的摄像头调用方式，需要适配

4. **Q: 会员验证失败？**
   A: 检查网络连接，确保设备时间正确

### 联系方式

- **GitHub Issues：** 提交 Bug 报告和功能请求
- **技术讨论：** 在项目 Discussions 中参与讨论
- **安全问题：** 通过私有渠道报告安全漏洞

## 更新日志

### v0.0.1 (当前版本)
- 基础虚拟摄像头功能
- 音量键触发机制
- 视频文件替换
- 远程视频下载
- 基础的设备兼容性

### 计划功能
- 会员系统集成
- 用户界面优化
- 性能提升
- 更多视频格式支持
- 跨平台扩展

---

**最后更新：** 2024年1月
**文档版本：** v1.0
**项目状态：** 开发中

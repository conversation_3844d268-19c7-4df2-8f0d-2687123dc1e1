# iOS-VCAM 传统云端会员系统设计方案

## 项目概述

基于iOS-VCAM虚拟摄像头项目的商业化会员系统设计，采用传统云端架构，提供完整的用户管理、支付验证和设备绑定功能。

## 1. .deb包部署和使用方式分析

### 1.1 部署机制分析

**包信息：**
- 包名：com.if-she.cydia.vcam
- 版本：0.0.1-1
- 架构：iphoneos-arm
- 依赖：mobilesubstrate (>= 0.9.5000)

**安装流程：**

```bash
# 方式1：通过Cydia安装（推荐）
1. 将.deb文件上传到Cydia源
2. 用户通过Cydia搜索安装
3. 自动处理依赖关系和权限

# 方式2：手动安装（需要SSH访问）
scp com.if-she.cydia.vcam_0.0.1-1_iphoneos-arm.deb root@device_ip:/tmp/
ssh root@device_ip
dpkg -i /tmp/com.if-she.cydia.vcam_0.0.1-1_iphoneos-arm.deb
killall SpringBoard  # 重启SpringBoard加载插件
```

### 1.2 工作机制

**系统级Tweak插件特点：**
- 全局注入到所有使用AVFoundation的进程
- 系统级Hook拦截摄像头API调用
- 无独立界面，通过音量键触发

**第三方App使用流程：**
1. 启动目标App（微信、抖音等）
2. 进入摄像头界面
3. 激活虚拟摄像头：
   - 完整模式：音量+ → 音量-（1秒内）
   - 便捷模式：音量- → 音量+（1秒内）
4. 选择替换视频或远程下载
5. 实时预览替换效果

### 1.3 商业化改进建议

**当前音量键机制问题：**
- 用户体验不直观
- 容易误触发
- 不适合商业产品

**改进方案：**

```objective-c
// 建议1：悬浮按钮触发
@interface VCAMFloatingButton : UIButton
@property (nonatomic, strong) UIPanGestureRecognizer *panGesture;
@end

@implementation VCAMFloatingButton
- (void)setupFloatingButton {
    self.frame = CGRectMake([UIScreen mainScreen].bounds.size.width - 60, 100, 50, 50);
    self.backgroundColor = [[UIColor blackColor] colorWithAlphaComponent:0.7];
    self.layer.cornerRadius = 25;
    [self setTitle:@"📹" forState:UIControlStateNormal];
    
    [self addTarget:self action:@selector(showMenu) forControlEvents:UIControlEventTouchUpInside];
}
@end

// 建议2：手势触发机制
%hook UIWindow
- (void)sendEvent:(UIEvent *)event {
    if (event.type == UIEventTypeTouches) {
        NSSet *touches = [event allTouches];
        if (touches.count == 3) { // 三指点击触发
            [self showVCAMMenu];
            return;
        }
    }
    %orig;
}
%end
```

## 2. 会员系统数据库设计

### 2.1 数据库表结构

```sql
-- 用户基础信息表
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    udid VARCHAR(40) UNIQUE NOT NULL COMMENT '设备UDID',
    device_model VARCHAR(50) NOT NULL COMMENT '设备型号',
    ios_version VARCHAR(20) NOT NULL COMMENT 'iOS版本',
    app_version VARCHAR(20) NOT NULL COMMENT 'VCAM版本',
    jailbreak_tool VARCHAR(30) COMMENT '越狱工具',
    first_install_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_active_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    country_code VARCHAR(5) COMMENT '国家代码',
    timezone VARCHAR(50) COMMENT '时区',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 会员订阅表
CREATE TABLE subscriptions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    subscription_type ENUM('trial', 'monthly', 'quarterly', 'yearly') NOT NULL,
    status ENUM('active', 'expired', 'cancelled', 'pending') NOT NULL,
    start_date TIMESTAMP NOT NULL,
    end_date TIMESTAMP NOT NULL,
    purchase_token VARCHAR(255) COMMENT '购买凭证',
    payment_method ENUM('apple_pay', 'alipay', 'wechat', 'crypto') NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_status (user_id, status),
    INDEX idx_end_date (end_date)
);

-- 使用统计表
CREATE TABLE usage_stats (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    target_app VARCHAR(100) NOT NULL COMMENT '目标应用Bundle ID',
    usage_duration INT NOT NULL COMMENT '使用时长(秒)',
    video_count INT DEFAULT 1 COMMENT '使用视频数量',
    session_date DATE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_date (user_id, session_date),
    INDEX idx_app_stats (target_app, session_date)
);

-- 设备指纹表（防刷机制）
CREATE TABLE device_fingerprints (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    hardware_uuid VARCHAR(40) NOT NULL COMMENT '硬件UUID',
    serial_number VARCHAR(50) COMMENT '序列号',
    mac_address VARCHAR(17) COMMENT 'WiFi MAC地址',
    cpu_type VARCHAR(20) COMMENT 'CPU类型',
    total_memory BIGINT COMMENT '总内存',
    disk_space BIGINT COMMENT '磁盘空间',
    fingerprint_hash VARCHAR(64) UNIQUE NOT NULL COMMENT '设备指纹哈希',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_fingerprint (fingerprint_hash)
);

-- 验证令牌表
CREATE TABLE auth_tokens (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    token_hash VARCHAR(64) UNIQUE NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    is_revoked BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_token_expires (token_hash, expires_at, is_revoked)
);
```

### 2.2 防刷机制设计

```objective-c
// 设备指纹生成
@interface VCAMDeviceFingerprint : NSObject
+ (NSString *)generateDeviceFingerprint;
+ (BOOL)validateDeviceIntegrity;
@end

@implementation VCAMDeviceFingerprint
+ (NSString *)generateDeviceFingerprint {
    NSMutableString *fingerprint = [NSMutableString string];
    
    // 硬件信息
    [fingerprint appendString:[self getHardwareUUID]];
    [fingerprint appendString:[self getCPUType]];
    [fingerprint appendString:[self getTotalMemory]];
    [fingerprint appendString:[self getDiskSpace]];
    
    // 系统信息
    [fingerprint appendString:[[UIDevice currentDevice] systemVersion]];
    [fingerprint appendString:[[UIDevice currentDevice] model]];
    
    // 越狱环境信息
    [fingerprint appendString:[self getJailbreakSignature]];
    
    return [self sha256:fingerprint];
}

+ (NSString *)getJailbreakSignature {
    NSMutableString *signature = [NSMutableString string];
    
    // 检查常见越狱文件
    NSArray *jailbreakPaths = @[
        @"/Applications/Cydia.app",
        @"/usr/sbin/sshd",
        @"/bin/bash",
        @"/etc/apt"
    ];
    
    for (NSString *path in jailbreakPaths) {
        if ([[NSFileManager defaultManager] fileExistsAtPath:path]) {
            [signature appendString:[path lastPathComponent]];
        }
    }
    
    return signature;
}
@end
```

### 2.3 数据合规性考虑

```objective-c
// GDPR合规数据收集
@interface VCAMDataCompliance : NSObject
+ (void)requestDataCollectionConsent;
+ (void)anonymizeUserData:(NSString *)udid;
+ (void)exportUserData:(NSString *)udid completion:(void(^)(NSDictionary *data))completion;
+ (void)deleteUserData:(NSString *)udid;
@end

@implementation VCAMDataCompliance
+ (void)requestDataCollectionConsent {
    UIAlertController *alert = [UIAlertController 
        alertControllerWithTitle:@"数据收集授权" 
        message:@"为了提供更好的服务，我们需要收集设备基础信息。您可以随时撤销授权。" 
        preferredStyle:UIAlertControllerStyleAlert];
    
    [alert addAction:[UIAlertAction actionWithTitle:@"同意" style:UIAlertActionStyleDefault handler:^(UIAlertAction *action) {
        [[NSUserDefaults standardUserDefaults] setBool:YES forKey:@"VCAMDataConsentGiven"];
    }]];
    
    [alert addAction:[UIAlertAction actionWithTitle:@"拒绝" style:UIAlertActionStyleCancel handler:^(UIAlertAction *action) {
        [[NSUserDefaults standardUserDefaults] setBool:NO forKey:@"VCAMDataConsentGiven"];
    }]];
}
@end
```

## 3. 会员校验集成方案

### 3.1 双重验证策略

```objective-c
// 会员验证管理器
@interface VCAMSubscriptionManager : NSObject
@property (nonatomic, strong) NSString *currentToken;
@property (nonatomic, assign) BOOL isSubscriptionValid;
@property (nonatomic, strong) NSDate *lastValidationTime;

+ (instancetype)sharedManager;
- (void)validateSubscriptionWithCompletion:(void(^)(BOOL isValid, NSError *error))completion;
- (BOOL)shouldAllowFeatureUsage;
- (void)handleSubscriptionExpired;
@end

@implementation VCAMSubscriptionManager

+ (instancetype)sharedManager {
    static VCAMSubscriptionManager *manager = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        manager = [[VCAMSubscriptionManager alloc] init];
    });
    return manager;
}

- (void)validateSubscriptionWithCompletion:(void(^)(BOOL isValid, NSError *error))completion {
    // 本地验证
    if (![self performLocalValidation]) {
        completion(NO, [NSError errorWithDomain:@"VCAMError" code:1001 userInfo:@{NSLocalizedDescriptionKey: @"本地验证失败"}]);
        return;
    }
    
    // 云端验证
    [self performCloudValidationWithCompletion:^(BOOL cloudValid, NSError *error) {
        if (error) {
            // 网络错误时使用本地缓存结果
            BOOL localValid = [self isLocalSubscriptionValid];
            completion(localValid, nil);
        } else {
            self.isSubscriptionValid = cloudValid;
            [self updateLocalSubscriptionCache:cloudValid];
            completion(cloudValid, nil);
        }
    }];
}

- (BOOL)performLocalValidation {
    // 检查设备指纹
    NSString *currentFingerprint = [VCAMDeviceFingerprint generateDeviceFingerprint];
    NSString *storedFingerprint = [[NSUserDefaults standardUserDefaults] stringForKey:@"VCAMDeviceFingerprint"];
    
    if (![currentFingerprint isEqualToString:storedFingerprint]) {
        NSLog(@"设备指纹验证失败");
        return NO;
    }
    
    // 检查本地令牌
    NSString *localToken = [[NSUserDefaults standardUserDefaults] stringForKey:@"VCAMAuthToken"];
    NSDate *tokenExpiry = [[NSUserDefaults standardUserDefaults] objectForKey:@"VCAMTokenExpiry"];
    
    if (!localToken || !tokenExpiry || [tokenExpiry timeIntervalSinceNow] < 0) {
        NSLog(@"本地令牌无效或已过期");
        return NO;
    }
    
    return YES;
}
@end
```

### 3.2 Hook方法集成点

```objective-c
// 在关键Hook点添加会员验证
%hook AVCaptureVideoDataOutput
- (void)setSampleBufferDelegate:(id<AVCaptureVideoDataOutputSampleBufferDelegate>)sampleBufferDelegate queue:(dispatch_queue_t)sampleBufferCallbackQueue{
    
    // 会员验证检查点1：视频数据输出设置时
    if (![[VCAMSubscriptionManager sharedManager] shouldAllowFeatureUsage]) {
        [self showSubscriptionRequiredAlert];
        return %orig; // 不进行Hook，使用原始功能
    }
    
    // 原有Hook逻辑...
    %orig;
}
%end

%hook AVCaptureStillImageOutput
- (void)captureStillImageAsynchronouslyFromConnection:(AVCaptureConnection *)connection completionHandler:(void (^)(CMSampleBufferRef imageDataSampleBuffer, NSError *error))handler{
    
    // 会员验证检查点2：拍照时
    if (![[VCAMSubscriptionManager sharedManager] shouldAllowFeatureUsage]) {
        [self showSubscriptionRequiredAlert];
        return %orig(connection, handler);
    }
    
    // 原有Hook逻辑...
    %orig(connection, [newHandler copy]);
}
%end

// 音量键触发时的验证
%hook VolumeControl
-(void)decreaseVolume {
    NSTimeInterval nowtime = [[NSDate date] timeIntervalSince1970];
    if (g_volume_up_time != 0 && nowtime - g_volume_up_time < 1) {
        
        // 会员验证检查点3：功能菜单触发时
        [[VCAMSubscriptionManager sharedManager] validateSubscriptionWithCompletion:^(BOOL isValid, NSError *error) {
            dispatch_async(dispatch_get_main_queue(), ^{
                if (isValid) {
                    [self showVCAMMainMenu]; // 显示功能菜单
                } else {
                    [self showSubscriptionExpiredAlert]; // 显示订阅过期提示
                }
            });
        }];
        return;
    }
    %orig;
}
%end
```

### 3.3 用户体验优化

```objective-c
// 优雅的订阅提示界面
@interface VCAMSubscriptionAlert : NSObject
+ (void)showTrialExpiredAlert;
+ (void)showSubscriptionRequiredAlert;
+ (void)showNetworkErrorAlert;
@end

@implementation VCAMSubscriptionAlert

+ (void)showSubscriptionRequiredAlert {
    UIAlertController *alert = [UIAlertController 
        alertControllerWithTitle:@"VCAM Pro" 
        message:@"此功能需要VCAM Pro会员\n\n🎯 无限制使用所有功能\n📱 支持所有App\n🔄 实时视频替换\n☁️ 云端视频库" 
        preferredStyle:UIAlertControllerStyleAlert];
    
    // 购买按钮
    [alert addAction:[UIAlertAction actionWithTitle:@"立即购买 ¥12/月" style:UIAlertActionStyleDefault handler:^(UIAlertAction *action) {
        [self openSubscriptionPage];
    }]];
    
    // 恢复购买
    [alert addAction:[UIAlertAction actionWithTitle:@"恢复购买" style:UIAlertActionStyleDefault handler:^(UIAlertAction *action) {
        [self restorePurchases];
    }]];
    
    // 稍后再说
    [alert addAction:[UIAlertAction actionWithTitle:@"稍后再说" style:UIAlertActionStyleCancel handler:nil]];
    
    [[GetFrame getKeyWindow].rootViewController presentViewController:alert animated:YES completion:nil];
}

+ (void)openSubscriptionPage {
    // 打开内购页面或外部购买链接
    NSURL *subscriptionURL = [NSURL URLWithString:@"https://vcam.app/subscribe"];
    [[UIApplication sharedApplication] openURL:subscriptionURL];
}
@end
```

## 4. 技术架构总结

### 4.1 系统架构图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   iOS设备       │    │   云端服务器     │    │   支付系统      │
│                 │    │                 │    │                 │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │ VCAM Tweak  │ │◄──►│ │ 验证服务    │ │◄──►│ │ 支付网关    │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │ 本地验证    │ │    │ │ 用户数据库  │ │    │ │ 订单管理    │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │                 │
│ │ 设备指纹    │ │    │ │ 设备管理    │ │    │                 │
│ └─────────────┘ │    │ └─────────────┘ │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 4.2 核心优势

1. **完整的会员体系**：支持试用、月度、季度、年度订阅
2. **严格的设备绑定**：防止账号共享和破解
3. **双重验证机制**：本地+云端验证确保安全性
4. **优雅的用户体验**：友好的提示界面和购买流程
5. **数据合规性**：符合GDPR等数据保护法规

### 4.3 部署建议

1. **服务器部署**：使用云服务商（AWS、阿里云等）
2. **数据库选择**：MySQL或PostgreSQL
3. **支付集成**：支持Apple Pay、支付宝、微信支付
4. **CDN加速**：提高全球用户访问速度
5. **监控告警**：实时监控系统状态和异常

## 5. 风险评估与应对

### 5.1 技术风险

- **破解风险**：通过代码混淆和反调试技术降低
- **兼容性风险**：定期测试新iOS版本兼容性
- **性能风险**：优化Hook逻辑，减少性能影响

### 5.2 商业风险

- **法律风险**：确保符合各国法律法规
- **竞争风险**：持续创新和功能优化
- **用户流失风险**：提供优质服务和技术支持

### 5.3 应对策略

- **定期安全审计**：检查系统安全漏洞
- **用户反馈机制**：及时响应用户需求
- **技术支持团队**：提供专业技术支持
- **法律咨询**：定期咨询法律专家意见

## 6. 服务端API设计

### 6.1 RESTful API接口

```python
# Flask服务端API示例
from flask import Flask, request, jsonify
from flask_sqlalchemy import SQLAlchemy
import hashlib
import jwt
from datetime import datetime, timedelta

app = Flask(__name__)
app.config['SQLALCHEMY_DATABASE_URI'] = 'mysql://user:pass@localhost/vcam_db'
db = SQLAlchemy(app)

# 用户验证API
@app.route('/api/v1/auth/verify', methods=['POST'])
def verify_subscription():
    data = request.get_json()

    udid = data.get('udid')
    device_fingerprint = data.get('device_fingerprint')
    auth_token = data.get('auth_token')

    # 验证设备指纹
    user = User.query.filter_by(udid=udid).first()
    if not user:
        return jsonify({'valid': False, 'error': 'User not found'}), 404

    # 检查设备指纹
    fingerprint = DeviceFingerprint.query.filter_by(
        user_id=user.id,
        fingerprint_hash=device_fingerprint,
        is_active=True
    ).first()

    if not fingerprint:
        return jsonify({'valid': False, 'error': 'Device not authorized'}), 403

    # 检查订阅状态
    subscription = Subscription.query.filter_by(
        user_id=user.id,
        status='active'
    ).filter(Subscription.end_date > datetime.utcnow()).first()

    if not subscription:
        return jsonify({'valid': False, 'error': 'Subscription expired'}), 402

    # 生成新的访问令牌
    token = generate_access_token(user.id)

    return jsonify({
        'valid': True,
        'token': token,
        'expires_at': (datetime.utcnow() + timedelta(hours=24)).isoformat(),
        'subscription_type': subscription.subscription_type,
        'subscription_end': subscription.end_date.isoformat()
    })

# 设备注册API
@app.route('/api/v1/device/register', methods=['POST'])
def register_device():
    data = request.get_json()

    udid = data.get('udid')
    device_model = data.get('device_model')
    ios_version = data.get('ios_version')
    device_fingerprint = data.get('device_fingerprint')

    # 检查设备是否已注册
    existing_user = User.query.filter_by(udid=udid).first()
    if existing_user:
        return jsonify({'error': 'Device already registered'}), 409

    # 创建新用户
    user = User(
        udid=udid,
        device_model=device_model,
        ios_version=ios_version,
        app_version=data.get('app_version'),
        country_code=data.get('country_code'),
        timezone=data.get('timezone')
    )
    db.session.add(user)
    db.session.flush()

    # 创建设备指纹
    fingerprint = DeviceFingerprint(
        user_id=user.id,
        hardware_uuid=data.get('hardware_uuid'),
        serial_number=data.get('serial_number'),
        mac_address=data.get('mac_address'),
        cpu_type=data.get('cpu_type'),
        total_memory=data.get('total_memory'),
        disk_space=data.get('disk_space'),
        fingerprint_hash=device_fingerprint
    )
    db.session.add(fingerprint)

    # 创建试用订阅
    trial_subscription = Subscription(
        user_id=user.id,
        subscription_type='trial',
        status='active',
        start_date=datetime.utcnow(),
        end_date=datetime.utcnow() + timedelta(days=1),  # 1天试用
        payment_method='trial',
        amount=0.00,
        currency='USD'
    )
    db.session.add(trial_subscription)

    db.session.commit()

    return jsonify({
        'success': True,
        'user_id': user.id,
        'trial_expires': trial_subscription.end_date.isoformat()
    })

# 订阅购买API
@app.route('/api/v1/subscription/purchase', methods=['POST'])
def purchase_subscription():
    data = request.get_json()

    user_id = data.get('user_id')
    subscription_type = data.get('subscription_type')
    payment_token = data.get('payment_token')
    payment_method = data.get('payment_method')

    # 验证支付
    if not verify_payment(payment_token, payment_method):
        return jsonify({'error': 'Payment verification failed'}), 400

    # 计算订阅时长和价格
    duration_map = {
        'monthly': (30, 12.00),
        'quarterly': (90, 30.00),
        'yearly': (365, 100.00)
    }

    days, amount = duration_map.get(subscription_type, (30, 12.00))

    # 创建订阅记录
    subscription = Subscription(
        user_id=user_id,
        subscription_type=subscription_type,
        status='active',
        start_date=datetime.utcnow(),
        end_date=datetime.utcnow() + timedelta(days=days),
        purchase_token=payment_token,
        payment_method=payment_method,
        amount=amount,
        currency='USD'
    )
    db.session.add(subscription)
    db.session.commit()

    return jsonify({
        'success': True,
        'subscription_id': subscription.id,
        'expires_at': subscription.end_date.isoformat()
    })

def verify_payment(payment_token, payment_method):
    """验证支付凭证"""
    if payment_method == 'apple_pay':
        return verify_apple_pay_receipt(payment_token)
    elif payment_method == 'alipay':
        return verify_alipay_payment(payment_token)
    elif payment_method == 'wechat':
        return verify_wechat_payment(payment_token)
    return False

def generate_access_token(user_id):
    """生成JWT访问令牌"""
    payload = {
        'user_id': user_id,
        'exp': datetime.utcnow() + timedelta(hours=24),
        'iat': datetime.utcnow()
    }
    return jwt.encode(payload, app.config['SECRET_KEY'], algorithm='HS256')
```

### 6.2 数据库模型定义

```python
# SQLAlchemy模型定义
class User(db.Model):
    __tablename__ = 'users'

    id = db.Column(db.BigInteger, primary_key=True)
    udid = db.Column(db.String(40), unique=True, nullable=False)
    device_model = db.Column(db.String(50), nullable=False)
    ios_version = db.Column(db.String(20), nullable=False)
    app_version = db.Column(db.String(20), nullable=False)
    jailbreak_tool = db.Column(db.String(30))
    first_install_time = db.Column(db.DateTime, default=datetime.utcnow)
    last_active_time = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    country_code = db.Column(db.String(5))
    timezone = db.Column(db.String(50))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # 关联关系
    subscriptions = db.relationship('Subscription', backref='user', lazy=True)
    device_fingerprints = db.relationship('DeviceFingerprint', backref='user', lazy=True)
    usage_stats = db.relationship('UsageStats', backref='user', lazy=True)

class Subscription(db.Model):
    __tablename__ = 'subscriptions'

    id = db.Column(db.BigInteger, primary_key=True)
    user_id = db.Column(db.BigInteger, db.ForeignKey('users.id'), nullable=False)
    subscription_type = db.Column(db.Enum('trial', 'monthly', 'quarterly', 'yearly'), nullable=False)
    status = db.Column(db.Enum('active', 'expired', 'cancelled', 'pending'), nullable=False)
    start_date = db.Column(db.DateTime, nullable=False)
    end_date = db.Column(db.DateTime, nullable=False)
    purchase_token = db.Column(db.String(255))
    payment_method = db.Column(db.Enum('apple_pay', 'alipay', 'wechat', 'crypto'), nullable=False)
    amount = db.Column(db.Decimal(10, 2), nullable=False)
    currency = db.Column(db.String(3), default='USD')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class DeviceFingerprint(db.Model):
    __tablename__ = 'device_fingerprints'

    id = db.Column(db.BigInteger, primary_key=True)
    user_id = db.Column(db.BigInteger, db.ForeignKey('users.id'), nullable=False)
    hardware_uuid = db.Column(db.String(40), nullable=False)
    serial_number = db.Column(db.String(50))
    mac_address = db.Column(db.String(17))
    cpu_type = db.Column(db.String(20))
    total_memory = db.Column(db.BigInteger)
    disk_space = db.Column(db.BigInteger)
    fingerprint_hash = db.Column(db.String(64), unique=True, nullable=False)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class UsageStats(db.Model):
    __tablename__ = 'usage_stats'

    id = db.Column(db.BigInteger, primary_key=True)
    user_id = db.Column(db.BigInteger, db.ForeignKey('users.id'), nullable=False)
    target_app = db.Column(db.String(100), nullable=False)
    usage_duration = db.Column(db.Integer, nullable=False)
    video_count = db.Column(db.Integer, default=1)
    session_date = db.Column(db.Date, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
```

## 7. 客户端集成详细实现

### 7.1 网络请求管理

```objective-c
// 网络请求管理器
@interface VCAMNetworkManager : NSObject
@property (nonatomic, strong) NSString *baseURL;
@property (nonatomic, strong) NSString *apiKey;

+ (instancetype)sharedManager;
- (void)verifySubscription:(NSDictionary *)deviceInfo completion:(void(^)(BOOL valid, NSDictionary *response, NSError *error))completion;
- (void)registerDevice:(NSDictionary *)deviceInfo completion:(void(^)(BOOL success, NSDictionary *response, NSError *error))completion;
- (void)purchaseSubscription:(NSDictionary *)purchaseInfo completion:(void(^)(BOOL success, NSDictionary *response, NSError *error))completion;
- (void)reportUsage:(NSDictionary *)usageInfo completion:(void(^)(BOOL success, NSError *error))completion;
@end

@implementation VCAMNetworkManager

+ (instancetype)sharedManager {
    static VCAMNetworkManager *manager = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        manager = [[VCAMNetworkManager alloc] init];
        manager.baseURL = @"https://api.vcam.app";
        manager.apiKey = @"your_api_key_here";
    });
    return manager;
}

- (void)verifySubscription:(NSDictionary *)deviceInfo completion:(void(^)(BOOL valid, NSDictionary *response, NSError *error))completion {
    NSString *url = [NSString stringWithFormat:@"%@/api/v1/auth/verify", self.baseURL];
    NSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:[NSURL URLWithString:url]];
    request.HTTPMethod = @"POST";
    [request setValue:@"application/json" forHTTPHeaderField:@"Content-Type"];
    [request setValue:[NSString stringWithFormat:@"Bearer %@", self.apiKey] forHTTPHeaderField:@"Authorization"];

    NSError *jsonError;
    NSData *jsonData = [NSJSONSerialization dataWithJSONObject:deviceInfo options:0 error:&jsonError];
    if (jsonError) {
        completion(NO, nil, jsonError);
        return;
    }

    request.HTTPBody = jsonData;

    [[NSURLSession.sharedSession dataTaskWithRequest:request completionHandler:^(NSData *data, NSURLResponse *response, NSError *error) {
        if (error) {
            completion(NO, nil, error);
            return;
        }

        NSHTTPURLResponse *httpResponse = (NSHTTPURLResponse *)response;
        NSError *parseError;
        NSDictionary *responseDict = [NSJSONSerialization JSONObjectWithData:data options:0 error:&parseError];

        if (parseError) {
            completion(NO, nil, parseError);
            return;
        }

        BOOL valid = httpResponse.statusCode == 200 && [responseDict[@"valid"] boolValue];
        completion(valid, responseDict, nil);
    }] resume];
}

- (void)registerDevice:(NSDictionary *)deviceInfo completion:(void(^)(BOOL success, NSDictionary *response, NSError *error))completion {
    NSString *url = [NSString stringWithFormat:@"%@/api/v1/device/register", self.baseURL];
    NSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:[NSURL URLWithString:url]];
    request.HTTPMethod = @"POST";
    [request setValue:@"application/json" forHTTPHeaderField:@"Content-Type"];
    [request setValue:[NSString stringWithFormat:@"Bearer %@", self.apiKey] forHTTPHeaderField:@"Authorization"];

    NSError *jsonError;
    NSData *jsonData = [NSJSONSerialization dataWithJSONObject:deviceInfo options:0 error:&jsonError];
    if (jsonError) {
        completion(NO, nil, jsonError);
        return;
    }

    request.HTTPBody = jsonData;

    [[NSURLSession.sharedSession dataTaskWithRequest:request completionHandler:^(NSData *data, NSURLResponse *response, NSError *error) {
        if (error) {
            completion(NO, nil, error);
            return;
        }

        NSHTTPURLResponse *httpResponse = (NSHTTPURLResponse *)response;
        NSError *parseError;
        NSDictionary *responseDict = [NSJSONSerialization JSONObjectWithData:data options:0 error:&parseError];

        if (parseError) {
            completion(NO, nil, parseError);
            return;
        }

        BOOL success = httpResponse.statusCode == 200 && [responseDict[@"success"] boolValue];
        completion(success, responseDict, nil);
    }] resume];
}

- (void)reportUsage:(NSDictionary *)usageInfo completion:(void(^)(BOOL success, NSError *error))completion {
    NSString *url = [NSString stringWithFormat:@"%@/api/v1/usage/report", self.baseURL];
    NSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:[NSURL URLWithString:url]];
    request.HTTPMethod = @"POST";
    [request setValue:@"application/json" forHTTPHeaderField:@"Content-Type"];
    [request setValue:[NSString stringWithFormat:@"Bearer %@", self.apiKey] forHTTPHeaderField:@"Authorization"];

    NSError *jsonError;
    NSData *jsonData = [NSJSONSerialization dataWithJSONObject:usageInfo options:0 error:&jsonError];
    if (jsonError) {
        completion(NO, jsonError);
        return;
    }

    request.HTTPBody = jsonData;

    [[NSURLSession.sharedSession dataTaskWithRequest:request completionHandler:^(NSData *data, NSURLResponse *response, NSError *error) {
        if (error) {
            completion(NO, error);
            return;
        }

        NSHTTPURLResponse *httpResponse = (NSHTTPURLResponse *)response;
        BOOL success = httpResponse.statusCode == 200;
        completion(success, nil);
    }] resume];
}
@end
```

### 7.2 完整的订阅管理器实现

```objective-c
// 完整的订阅管理器
@implementation VCAMSubscriptionManager

- (instancetype)init {
    self = [super init];
    if (self) {
        [self loadLocalSubscriptionData];
        [self schedulePeriodicValidation];
        [self registerForAppStateNotifications];
    }
    return self;
}

- (void)loadLocalSubscriptionData {
    // 从Keychain加载本地订阅数据
    NSDictionary *query = @{
        (__bridge id)kSecClass: (__bridge id)kSecClassGenericPassword,
        (__bridge id)kSecAttrService: @"com.vcam.subscription",
        (__bridge id)kSecAttrAccount: @"subscription_data",
        (__bridge id)kSecReturnData: @YES
    };

    CFDataRef dataRef = NULL;
    OSStatus status = SecItemCopyMatching((__bridge CFDictionaryRef)query, (CFTypeRef *)&dataRef);

    if (status == errSecSuccess && dataRef) {
        NSData *data = (__bridge_transfer NSData *)dataRef;
        NSError *error;
        NSDictionary *subscriptionData = [NSJSONSerialization JSONObjectWithData:data options:0 error:&error];

        if (!error && subscriptionData) {
            self.currentToken = subscriptionData[@"token"];
            self.isSubscriptionValid = [subscriptionData[@"is_valid"] boolValue];
            self.lastValidationTime = [NSDate dateWithTimeIntervalSince1970:[subscriptionData[@"last_validation"] doubleValue]];
        }
    }
}

- (void)saveLocalSubscriptionData {
    NSDictionary *subscriptionData = @{
        @"token": self.currentToken ?: @"",
        @"is_valid": @(self.isSubscriptionValid),
        @"last_validation": @([self.lastValidationTime timeIntervalSince1970])
    };

    NSError *error;
    NSData *data = [NSJSONSerialization dataWithJSONObject:subscriptionData options:0 error:&error];
    if (error) return;

    NSDictionary *query = @{
        (__bridge id)kSecClass: (__bridge id)kSecClassGenericPassword,
        (__bridge id)kSecAttrService: @"com.vcam.subscription",
        (__bridge id)kSecAttrAccount: @"subscription_data",
        (__bridge id)kSecValueData: data,
        (__bridge id)kSecAttrAccessible: (__bridge id)kSecAttrAccessibleWhenUnlockedThisDeviceOnly
    };

    SecItemDelete((__bridge CFDictionaryRef)query);
    SecItemAdd((__bridge CFDictionaryRef)query, NULL);
}

- (void)schedulePeriodicValidation {
    // 每小时验证一次订阅状态
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_BACKGROUND, 0), ^{
        while (YES) {
            [self validateSubscriptionWithCompletion:^(BOOL isValid, NSError *error) {
                if (error) {
                    NSLog(@"订阅验证失败: %@", error.localizedDescription);
                }
            }];
            sleep(3600); // 1小时
        }
    });
}

- (void)registerForAppStateNotifications {
    // 监听应用状态变化
    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(applicationDidBecomeActive)
                                                 name:UIApplicationDidBecomeActiveNotification
                                               object:nil];

    [[NSNotificationCenter defaultCenter] addObserver:self
                                             selector:@selector(applicationWillResignActive)
                                                 name:UIApplicationWillResignActiveNotification
                                               object:nil];
}

- (void)applicationDidBecomeActive {
    // 应用激活时验证订阅
    [self validateSubscriptionWithCompletion:nil];
}

- (void)applicationWillResignActive {
    // 应用进入后台时保存数据
    [self saveLocalSubscriptionData];
}

- (void)performCloudValidationWithCompletion:(void(^)(BOOL isValid, NSError *error))completion {
    NSString *udid = [[[UIDevice currentDevice] identifierForVendor] UUIDString];
    NSString *deviceFingerprint = [VCAMDeviceFingerprint generateDeviceFingerprint];

    NSDictionary *deviceInfo = @{
        @"udid": udid,
        @"device_fingerprint": deviceFingerprint,
        @"auth_token": self.currentToken ?: @"",
        @"app_version": [[NSBundle mainBundle] objectForInfoDictionaryKey:@"CFBundleShortVersionString"],
        @"ios_version": [[UIDevice currentDevice] systemVersion],
        @"device_model": [[UIDevice currentDevice] model],
        @"timestamp": @([[NSDate date] timeIntervalSince1970])
    };

    [[VCAMNetworkManager sharedManager] verifySubscription:deviceInfo completion:^(BOOL valid, NSDictionary *response, NSError *error) {
        if (completion) {
            completion(valid, error);
        }

        if (valid && response) {
            // 更新本地令牌
            self.currentToken = response[@"token"];
            self.lastValidationTime = [NSDate date];
            [self saveLocalSubscriptionData];

            // 报告使用统计
            [self reportCurrentUsage];
        }
    }];
}

- (void)reportCurrentUsage {
    NSString *bundleID = [[NSBundle mainBundle] bundleIdentifier];
    NSString *processName = [[NSProcessInfo processInfo] processName];

    NSDictionary *usageInfo = @{
        @"udid": [[[UIDevice currentDevice] identifierForVendor] UUIDString],
        @"target_app": processName,
        @"usage_duration": @(60), // 假设使用1分钟
        @"video_count": @(1),
        @"session_date": [[NSDateFormatter new] stringFromDate:[NSDate date]],
        @"timestamp": @([[NSDate date] timeIntervalSince1970])
    };

    [[VCAMNetworkManager sharedManager] reportUsage:usageInfo completion:^(BOOL success, NSError *error) {
        if (!success && error) {
            NSLog(@"使用统计报告失败: %@", error.localizedDescription);
        }
    }];
}

- (BOOL)shouldAllowFeatureUsage {
    // 检查本地验证
    if (![self performLocalValidation]) {
        return NO;
    }

    // 检查上次验证时间
    NSTimeInterval timeSinceLastValidation = [[NSDate date] timeIntervalSinceDate:self.lastValidationTime];
    if (timeSinceLastValidation > 24 * 60 * 60) { // 超过24小时
        // 需要重新验证
        [self validateSubscriptionWithCompletion:nil];
        return self.isSubscriptionValid; // 使用缓存的状态
    }

    return self.isSubscriptionValid;
}

- (void)handleSubscriptionExpired {
    self.isSubscriptionValid = NO;
    [self saveLocalSubscriptionData];

    // 显示续费提示
    dispatch_async(dispatch_get_main_queue(), ^{
        [VCAMSubscriptionAlert showSubscriptionExpiredAlert];
    });
}

- (BOOL)isLocalSubscriptionValid {
    // 检查本地缓存的订阅状态
    NSTimeInterval timeSinceLastValidation = [[NSDate date] timeIntervalSinceDate:self.lastValidationTime];

    // 如果超过7天未验证，认为无效
    if (timeSinceLastValidation > 7 * 24 * 60 * 60) {
        return NO;
    }

    return self.isSubscriptionValid;
}

- (void)updateLocalSubscriptionCache:(BOOL)isValid {
    self.isSubscriptionValid = isValid;
    self.lastValidationTime = [NSDate date];
    [self saveLocalSubscriptionData];
}

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}
@end
```

## 8. 部署和运维

### 8.1 服务器部署架构

```yaml
# Docker Compose部署配置
version: '3.8'

services:
  # Web应用服务
  vcam-api:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=mysql://vcam_user:password@db:3306/vcam_db
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=your_secret_key_here
    depends_on:
      - db
      - redis
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped

  # 数据库服务
  db:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=root_password
      - MYSQL_DATABASE=vcam_db
      - MYSQL_USER=vcam_user
      - MYSQL_PASSWORD=password
    volumes:
      - mysql_data:/var/lib/mysql
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "3306:3306"
    restart: unless-stopped

  # Redis缓存服务
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - vcam-api
    restart: unless-stopped

  # 监控服务
  prometheus:
    image: prom/prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    restart: unless-stopped

  grafana:
    image: grafana/grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
    restart: unless-stopped

volumes:
  mysql_data:
  redis_data:
  prometheus_data:
  grafana_data:
```

### 8.2 监控和告警

```python
# 监控指标收集
from prometheus_client import Counter, Histogram, Gauge, generate_latest
import time

# 定义监控指标
REQUEST_COUNT = Counter('vcam_requests_total', 'Total requests', ['method', 'endpoint', 'status'])
REQUEST_DURATION = Histogram('vcam_request_duration_seconds', 'Request duration')
ACTIVE_SUBSCRIPTIONS = Gauge('vcam_active_subscriptions', 'Number of active subscriptions')
DEVICE_REGISTRATIONS = Counter('vcam_device_registrations_total', 'Total device registrations')

# 中间件：记录请求指标
@app.before_request
def before_request():
    request.start_time = time.time()

@app.after_request
def after_request(response):
    request_duration = time.time() - request.start_time
    REQUEST_DURATION.observe(request_duration)
    REQUEST_COUNT.labels(
        method=request.method,
        endpoint=request.endpoint or 'unknown',
        status=response.status_code
    ).inc()
    return response

# 监控端点
@app.route('/metrics')
def metrics():
    return generate_latest()

# 健康检查端点
@app.route('/health')
def health_check():
    try:
        # 检查数据库连接
        db.session.execute('SELECT 1')

        # 检查Redis连接
        redis_client.ping()

        return jsonify({'status': 'healthy', 'timestamp': datetime.utcnow().isoformat()})
    except Exception as e:
        return jsonify({'status': 'unhealthy', 'error': str(e)}), 500

# 定期更新指标
def update_metrics():
    while True:
        try:
            # 更新活跃订阅数
            active_count = Subscription.query.filter(
                Subscription.status == 'active',
                Subscription.end_date > datetime.utcnow()
            ).count()
            ACTIVE_SUBSCRIPTIONS.set(active_count)

            time.sleep(60)  # 每分钟更新一次
        except Exception as e:
            print(f"更新指标失败: {e}")
            time.sleep(60)

# 启动指标更新线程
import threading
metrics_thread = threading.Thread(target=update_metrics, daemon=True)
metrics_thread.start()
```

### 8.3 自动化部署脚本

```bash
#!/bin/bash
# 自动化部署脚本

set -e

# 配置变量
PROJECT_NAME="vcam-api"
DOCKER_REGISTRY="your-registry.com"
VERSION=$(git rev-parse --short HEAD)
ENVIRONMENT=${1:-production}

echo "开始部署 $PROJECT_NAME 版本 $VERSION 到 $ENVIRONMENT 环境"

# 1. 构建Docker镜像
echo "构建Docker镜像..."
docker build -t $DOCKER_REGISTRY/$PROJECT_NAME:$VERSION .
docker tag $DOCKER_REGISTRY/$PROJECT_NAME:$VERSION $DOCKER_REGISTRY/$PROJECT_NAME:latest

# 2. 推送到镜像仓库
echo "推送镜像到仓库..."
docker push $DOCKER_REGISTRY/$PROJECT_NAME:$VERSION
docker push $DOCKER_REGISTRY/$PROJECT_NAME:latest

# 3. 更新部署配置
echo "更新部署配置..."
sed -i "s|image: .*$PROJECT_NAME:.*|image: $DOCKER_REGISTRY/$PROJECT_NAME:$VERSION|g" docker-compose.$ENVIRONMENT.yml

# 4. 数据库迁移
echo "执行数据库迁移..."
docker-compose -f docker-compose.$ENVIRONMENT.yml run --rm vcam-api python manage.py db upgrade

# 5. 部署服务
echo "部署服务..."
docker-compose -f docker-compose.$ENVIRONMENT.yml up -d

# 6. 健康检查
echo "等待服务启动..."
sleep 30

for i in {1..10}; do
    if curl -f http://localhost/health; then
        echo "服务部署成功！"
        break
    else
        echo "等待服务启动... ($i/10)"
        sleep 10
    fi

    if [ $i -eq 10 ]; then
        echo "服务启动失败！"
        exit 1
    fi
done

# 7. 清理旧镜像
echo "清理旧镜像..."
docker image prune -f

echo "部署完成！"
```

## 9. 安全性增强

### 9.1 API安全防护

```python
# API安全中间件
from functools import wraps
import hashlib
import hmac
import time

def require_api_key(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        api_key = request.headers.get('Authorization')
        if not api_key or not api_key.startswith('Bearer '):
            return jsonify({'error': 'Missing API key'}), 401

        key = api_key.replace('Bearer ', '')
        if not verify_api_key(key):
            return jsonify({'error': 'Invalid API key'}), 401

        return f(*args, **kwargs)
    return decorated_function

def rate_limit(max_requests=100, window=3600):
    """速率限制装饰器"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            client_ip = request.remote_addr
            key = f"rate_limit:{client_ip}:{f.__name__}"

            current_requests = redis_client.get(key)
            if current_requests and int(current_requests) >= max_requests:
                return jsonify({'error': 'Rate limit exceeded'}), 429

            # 增加请求计数
            pipe = redis_client.pipeline()
            pipe.incr(key)
            pipe.expire(key, window)
            pipe.execute()

            return f(*args, **kwargs)
        return decorated_function
    return decorator

def verify_signature(f):
    """验证请求签名"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        signature = request.headers.get('X-Signature')
        timestamp = request.headers.get('X-Timestamp')

        if not signature or not timestamp:
            return jsonify({'error': 'Missing signature'}), 401

        # 检查时间戳（防重放攻击）
        if abs(time.time() - float(timestamp)) > 300:  # 5分钟
            return jsonify({'error': 'Request expired'}), 401

        # 验证签名
        body = request.get_data()
        expected_signature = hmac.new(
            app.config['WEBHOOK_SECRET'].encode(),
            f"{timestamp}{body.decode()}".encode(),
            hashlib.sha256
        ).hexdigest()

        if not hmac.compare_digest(signature, expected_signature):
            return jsonify({'error': 'Invalid signature'}), 401

        return f(*args, **kwargs)
    return decorated_function

# 应用安全中间件
@app.route('/api/v1/auth/verify', methods=['POST'])
@require_api_key
@rate_limit(max_requests=1000, window=3600)
@verify_signature
def verify_subscription():
    # 原有验证逻辑
    pass
```

### 9.2 数据加密和脱敏

```python
# 数据加密工具
from cryptography.fernet import Fernet
import base64

class DataEncryption:
    def __init__(self, key=None):
        if key:
            self.cipher = Fernet(key)
        else:
            self.cipher = Fernet(Fernet.generate_key())

    def encrypt(self, data):
        """加密数据"""
        if isinstance(data, str):
            data = data.encode()
        return self.cipher.encrypt(data).decode()

    def decrypt(self, encrypted_data):
        """解密数据"""
        if isinstance(encrypted_data, str):
            encrypted_data = encrypted_data.encode()
        return self.cipher.decrypt(encrypted_data).decode()

# 敏感数据脱敏
def mask_sensitive_data(data, field_name):
    """脱敏敏感数据"""
    if field_name in ['udid', 'serial_number']:
        return data[:8] + '*' * (len(data) - 12) + data[-4:]
    elif field_name == 'mac_address':
        parts = data.split(':')
        return ':'.join(parts[:2] + ['**'] * (len(parts) - 4) + parts[-2:])
    elif field_name == 'ip_address':
        parts = data.split('.')
        return '.'.join(parts[:2] + ['***'] * (len(parts) - 2))
    return data

# 审计日志
class AuditLogger:
    @staticmethod
    def log_access(user_id, action, resource, ip_address):
        """记录访问日志"""
        log_entry = {
            'timestamp': datetime.utcnow().isoformat(),
            'user_id': user_id,
            'action': action,
            'resource': resource,
            'ip_address': mask_sensitive_data(ip_address, 'ip_address'),
            'user_agent': request.headers.get('User-Agent', '')
        }

        # 写入日志文件
        with open('/var/log/vcam/audit.log', 'a') as f:
            f.write(json.dumps(log_entry) + '\n')

    @staticmethod
    def log_payment(user_id, amount, payment_method, status):
        """记录支付日志"""
        log_entry = {
            'timestamp': datetime.utcnow().isoformat(),
            'user_id': user_id,
            'amount': amount,
            'payment_method': payment_method,
            'status': status,
            'ip_address': mask_sensitive_data(request.remote_addr, 'ip_address')
        }

        with open('/var/log/vcam/payment.log', 'a') as f:
            f.write(json.dumps(log_entry) + '\n')
```

---

*本文档提供了完整的传统云端会员系统设计方案，包含了从客户端到服务端的完整实现，适合需要稳定可靠的商业化产品。该方案具有良好的可扩展性、安全性和可维护性。*
